{"name": "prosemirror-collab", "version": "1.3.1", "description": "Collaborative editing for ProseMirror", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "sideEffects": false, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-collab.git"}, "dependencies": {"prosemirror-state": "^1.0.0"}, "devDependencies": {"@prosemirror/buildhelper": "^0.1.5", "prosemirror-history": "^1.0.0", "prosemirror-model": "^1.0.0", "prosemirror-test-builder": "^1.0.0", "prosemirror-transform": "^1.0.0"}, "scripts": {"test": "pm-runtests", "prepare": "pm-buildhelper src/collab.ts"}}