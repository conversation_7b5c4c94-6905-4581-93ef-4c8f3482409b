{"name": "prosemirror-schema-list", "version": "1.5.1", "description": "List-related schema elements and commands for ProseMirror", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "sideEffects": false, "license": "MIT", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "git://github.com/prosemirror/prosemirror-schema-list.git"}, "dependencies": {"prosemirror-model": "^1.0.0", "prosemirror-transform": "^1.7.3", "prosemirror-state": "^1.0.0"}, "devDependencies": {"prosemirror-state": "^1.0.0", "@prosemirror/buildhelper": "^0.1.5", "prosemirror-test-builder": "^1.0.0"}, "scripts": {"test": "pm-runtests", "prepare": "pm-buildhelper src/schema-list.ts"}}