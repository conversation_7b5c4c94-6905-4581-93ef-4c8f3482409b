import { ResolvedPos } from '@tiptap/pm/model';
/**
 * Returns the text content of a resolved prosemirror position
 * @param $from The resolved position to get the text content from
 * @param maxMatch The maximum number of characters to match
 * @returns The text content
 */
export declare const getTextContentFromNodes: ($from: ResolvedPos, maxMatch?: number) => string;
//# sourceMappingURL=getTextContentFromNodes.d.ts.map