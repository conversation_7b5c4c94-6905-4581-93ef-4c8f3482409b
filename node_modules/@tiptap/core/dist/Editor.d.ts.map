{"version": 3, "file": "Editor.d.ts", "sourceRoot": "", "sources": ["../src/Editor.ts"], "names": [], "mappings": "AACA,OAAO,EACL,QAAQ,EAER,QAAQ,EACR,MAAM,EACP,MAAM,kBAAkB,CAAA;AACzB,OAAO,EACL,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAC5C,MAAM,kBAAkB,CAAA;AACzB,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAA;AAG5C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAChD,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAA;AAaxD,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAA;AAEtC,OAAO,EACL,WAAW,EACX,eAAe,EACf,YAAY,EACZ,aAAa,EACb,WAAW,EACX,cAAc,EACd,cAAc,EACf,MAAM,YAAY,CAAA;AAInB,OAAO,KAAK,UAAU,MAAM,uBAAuB,CAAA;AAGnD,MAAM,WAAW,uBAAwB,SAAQ,WAAW;IAC1D,MAAM,CAAC,EAAE,MAAM,CAAA;CAChB;AAED,qBAAa,MAAO,SAAQ,YAAY,CAAC,YAAY,CAAC;IACpD,OAAO,CAAC,cAAc,CAAiB;IAEhC,gBAAgB,EAAG,gBAAgB,CAAA;IAE1C,OAAO,CAAC,GAAG,CAAmB;IAEvB,MAAM,EAAG,MAAM,CAAA;IAEf,IAAI,EAAG,UAAU,CAAA;IAEjB,SAAS,UAAQ;IAExB;;OAEG;IACI,aAAa,UAAQ;IAErB,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAK;IAE1C,OAAO,EAAE,aAAa,CA2B5B;gBAEW,OAAO,GAAE,OAAO,CAAC,aAAa,CAAM;IAgChD;;OAEG;IACH,IAAW,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAExC;IAED;;OAEG;IACH,IAAW,QAAQ,IAAI,cAAc,CAEpC;IAED;;OAEG;IACI,KAAK,IAAI,eAAe;IAI/B;;OAEG;IACI,GAAG,IAAI,WAAW;IAIzB;;OAEG;IACH,OAAO,CAAC,SAAS;IAMjB;;;;OAIG;IACI,UAAU,CAAC,OAAO,GAAE,OAAO,CAAC,aAAa,CAAM,GAAG,IAAI;IAiB7D;;OAEG;IACI,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,UAAO,GAAG,IAAI;IAQ9D;;OAEG;IACH,IAAW,UAAU,IAAI,OAAO,CAK/B;IAED;;OAEG;IACH,IAAW,KAAK,IAAI,WAAW,CAE9B;IAED;;;;;;OAMG;IACI,cAAc,CACnB,MAAM,EAAE,MAAM,EACd,aAAa,CAAC,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,MAAM,EAAE,GACjE,WAAW;IAYd;;;;;OAKG;IACI,gBAAgB,CAAC,uBAAuB,EAAE,MAAM,GAAG,SAAS,GAAG,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,GAAG,WAAW,GAAG,SAAS;IA8BtH;;OAEG;IACH,OAAO,CAAC,sBAAsB;IA0B9B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAM5B;;OAEG;IACH,OAAO,CAAC,YAAY;IAIpB;;OAEG;IACH,OAAO,CAAC,UAAU;IAyElB;;OAEG;IACI,eAAe,IAAI,IAAI;IAU9B;;OAEG;IACI,YAAY,IAAI,IAAI;IAIpB,sBAAsB,UAAQ;IAErC,OAAO,CAAC,mBAAmB,CAA2B;IAE/C,kBAAkB,CAAC,EAAE,EAAE,MAAM,IAAI;IAYxC;;;;OAIG;IACH,OAAO,CAAC,mBAAmB;IAqE3B;;OAEG;IACI,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAInF;;;;;OAKG;IACI,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,EAAE,GAAG,OAAO;IAChD,QAAQ,CAAC,UAAU,EAAE,EAAE,GAAG,OAAO;IASxC;;OAEG;IACI,OAAO,IAAI,WAAW;IAI7B;;OAEG;IACI,OAAO,IAAI,MAAM;IAIxB;;OAEG;IACI,OAAO,CAAC,OAAO,CAAC,EAAE;QACvB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,eAAe,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;KACjD,GAAG,MAAM;IAYV;;OAEG;IACH,IAAW,OAAO,IAAI,OAAO,CAE5B;IAED;;;;OAIG;IACI,iBAAiB,IAAI,MAAM;IAQlC;;OAEG;IACI,OAAO,IAAI,IAAI;IAiBtB;;OAEG;IACH,IAAW,WAAW,IAAI,OAAO,CAGhC;IAEM,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,GAAG,OAAO,GAAG,IAAI;IAI5E,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,GAAG,OAAO,EAAE,GAAG,IAAI;IAI/E,IAAI,CAAC,GAAG,EAAE,MAAM;IAMvB,IAAI,IAAI,YAEP;CACF"}