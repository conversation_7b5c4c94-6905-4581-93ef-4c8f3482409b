{"version": 3, "file": "index.umd.js", "sources": ["../../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.js", "../../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../../node_modules/use-sync-external-store/shim/index.js", "../src/EditorContent.tsx", "../../../node_modules/fast-deep-equal/es6/react.js", "../../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.production.min.js", "../../../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../../node_modules/use-sync-external-store/shim/with-selector.js", "../src/useEditorState.ts", "../src/useEditor.ts", "../src/Context.tsx", "../src/BubbleMenu.tsx", "../src/FloatingMenu.tsx", "../src/useReactNodeView.ts", "../src/NodeViewContent.tsx", "../src/NodeViewWrapper.tsx", "../src/ReactRenderer.tsx", "../src/ReactNodeViewRenderer.tsx"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var e=require(\"react\");function h(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var k=\"function\"===typeof Object.is?Object.is:h,l=e.useState,m=e.useEffect,n=e.useLayoutEffect,p=e.useDebugValue;function q(a,b){var d=b(),f=l({inst:{value:d,getSnapshot:b}}),c=f[0].inst,g=f[1];n(function(){c.value=d;c.getSnapshot=b;r(c)&&g({inst:c})},[a,d,b]);m(function(){r(c)&&g({inst:c});return a(function(){r(c)&&g({inst:c})})},[a]);p(d);return d}\nfunction r(a){var b=a.getSnapshot;a=a.value;try{var d=b();return!k(a,d)}catch(f){return!0}}function t(a,b){return b()}var u=\"undefined\"===typeof window||\"undefined\"===typeof window.document||\"undefined\"===typeof window.document.createElement?t:q;exports.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:u;\n", "/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\n// dispatch for CommonJS interop named imports.\n\nvar useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue;\nvar didWarnOld18Alpha = false;\nvar didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  {\n    if (!didWarnOld18Alpha) {\n      if (React.startTransition !== undefined) {\n        didWarnOld18Alpha = true;\n\n        error('You are using an outdated, pre-release alpha of React 18 that ' + 'does not support useSyncExternalStore. The ' + 'use-sync-external-store shim will not work correctly. Upgrade ' + 'to a newer pre-release.');\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  var value = getSnapshot();\n\n  {\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n\n      if (!objectIs(value, cachedValue)) {\n        error('The result of getSnapshot should be cached to avoid an infinite loop');\n\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  var _useState = useState({\n    inst: {\n      value: value,\n      getSnapshot: getSnapshot\n    }\n  }),\n      inst = _useState[0].inst,\n      forceUpdate = _useState[1]; // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n\n  useLayoutEffect(function () {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n  }, [subscribe, value, getSnapshot]);\n  useEffect(function () {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst: inst\n      });\n    }\n\n    var handleStoreChange = function () {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst: inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange);\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  var prevValue = inst.value;\n\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\nvar canUseDOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n\nvar isServerEnvironment = !canUseDOM;\n\nvar shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore;\nvar useSyncExternalStore$2 = React.useSyncExternalStore !== undefined ? React.useSyncExternalStore : shim;\n\nexports.useSyncExternalStore = useSyncExternalStore$2;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "import { Editor } from '@tiptap/core'\nimport React, {\n  ForwardedRef, forwardRef, HTMLProps, LegacyRef, MutableRefObject,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim'\n\nimport { ContentComponent, EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\n\nconst mergeRefs = <T extends HTMLDivElement>(\n  ...refs: Array<MutableRefObject<T> | LegacyRef<T> | undefined>\n) => {\n  return (node: T) => {\n    refs.forEach(ref => {\n      if (typeof ref === 'function') {\n        ref(node)\n      } else if (ref) {\n        (ref as MutableRefObject<T | null>).current = node\n      }\n    })\n  }\n}\n\n/**\n * This component renders all of the editor's node views.\n */\nconst Portals: React.FC<{ contentComponent: ContentComponent }> = ({\n  contentComponent,\n}) => {\n  // For performance reasons, we render the node view portals on state changes only\n  const renderers = useSyncExternalStore(\n    contentComponent.subscribe,\n    contentComponent.getSnapshot,\n    contentComponent.getServerSnapshot,\n  )\n\n  // This allows us to directly render the portals without any additional wrapper\n  return (\n    <>\n      {Object.values(renderers)}\n    </>\n  )\n}\n\nexport interface EditorContentProps extends HTMLProps<HTMLDivElement> {\n  editor: Editor | null;\n  innerRef?: ForwardedRef<HTMLDivElement | null>;\n}\n\nfunction getInstance(): ContentComponent {\n  const subscribers = new Set<() => void>()\n  let renderers: Record<string, React.ReactPortal> = {}\n\n  return {\n    /**\n     * Subscribe to the editor instance's changes.\n     */\n    subscribe(callback: () => void) {\n      subscribers.add(callback)\n      return () => {\n        subscribers.delete(callback)\n      }\n    },\n    getSnapshot() {\n      return renderers\n    },\n    getServerSnapshot() {\n      return renderers\n    },\n    /**\n     * Adds a new NodeView Renderer to the editor.\n     */\n    setRenderer(id: string, renderer: ReactRenderer) {\n      renderers = {\n        ...renderers,\n        [id]: ReactDOM.createPortal(renderer.reactElement, renderer.element, id),\n      }\n\n      subscribers.forEach(subscriber => subscriber())\n    },\n    /**\n     * Removes a NodeView Renderer from the editor.\n     */\n    removeRenderer(id: string) {\n      const nextRenderers = { ...renderers }\n\n      delete nextRenderers[id]\n      renderers = nextRenderers\n      subscribers.forEach(subscriber => subscriber())\n    },\n  }\n}\n\nexport class PureEditorContent extends React.Component<\n  EditorContentProps,\n  { hasContentComponentInitialized: boolean }\n> {\n  editorContentRef: React.RefObject<any>\n\n  initialized: boolean\n\n  unsubscribeToContentComponent?: () => void\n\n  constructor(props: EditorContentProps) {\n    super(props)\n    this.editorContentRef = React.createRef()\n    this.initialized = false\n\n    this.state = {\n      hasContentComponentInitialized: Boolean((props.editor as EditorWithContentComponent | null)?.contentComponent),\n    }\n  }\n\n  componentDidMount() {\n    this.init()\n  }\n\n  componentDidUpdate() {\n    this.init()\n  }\n\n  init() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (editor && !editor.isDestroyed && editor.options.element) {\n      if (editor.contentComponent) {\n        return\n      }\n\n      const element = this.editorContentRef.current\n\n      element.append(...editor.options.element.childNodes)\n\n      editor.setOptions({\n        element,\n      })\n\n      editor.contentComponent = getInstance()\n\n      // Has the content component been initialized?\n      if (!this.state.hasContentComponentInitialized) {\n        // Subscribe to the content component\n        this.unsubscribeToContentComponent = editor.contentComponent.subscribe(() => {\n          this.setState(prevState => {\n            if (!prevState.hasContentComponentInitialized) {\n              return {\n                hasContentComponentInitialized: true,\n              }\n            }\n            return prevState\n          })\n\n          // Unsubscribe to previous content component\n          if (this.unsubscribeToContentComponent) {\n            this.unsubscribeToContentComponent()\n          }\n        })\n      }\n\n      editor.createNodeViews()\n\n      this.initialized = true\n    }\n  }\n\n  componentWillUnmount() {\n    const editor = this.props.editor as EditorWithContentComponent | null\n\n    if (!editor) {\n      return\n    }\n\n    this.initialized = false\n\n    if (!editor.isDestroyed) {\n      editor.view.setProps({\n        nodeViews: {},\n      })\n    }\n\n    if (this.unsubscribeToContentComponent) {\n      this.unsubscribeToContentComponent()\n    }\n\n    editor.contentComponent = null\n\n    if (!editor.options.element.firstChild) {\n      return\n    }\n\n    const newElement = document.createElement('div')\n\n    newElement.append(...editor.options.element.childNodes)\n\n    editor.setOptions({\n      element: newElement,\n    })\n  }\n\n  render() {\n    const { editor, innerRef, ...rest } = this.props\n\n    return (\n      <>\n        <div ref={mergeRefs(innerRef, this.editorContentRef)} {...rest} />\n        {/* @ts-ignore */}\n        {editor?.contentComponent && <Portals contentComponent={editor.contentComponent} />}\n      </>\n    )\n  }\n}\n\n// EditorContent should be re-created whenever the Editor instance changes\nconst EditorContentWithKey = forwardRef<HTMLDivElement, EditorContentProps>(\n  (props: Omit<EditorContentProps, 'innerRef'>, ref) => {\n    const key = React.useMemo(() => {\n      return Math.floor(Math.random() * 0xffffffff).toString()\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [props.editor])\n\n    // Can't use JSX here because it conflicts with the type definition of Vue's JSX, so use createElement\n    return React.createElement(PureEditorContent, {\n      key,\n      innerRef: ref,\n      ...props,\n    })\n  },\n)\n\nexport const EditorContent = React.memo(EditorContentWithKey)\n", "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n  var envHasBigInt64Array = typeof BigInt64Array !== 'undefined';\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n    if ((a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      for (i of a.entries())\n        if (!equal(i[1], b.get(i[0]))) return false;\n      return true;\n    }\n\n    if ((a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      for (i of a.entries())\n        if (!b.has(i[0])) return false;\n      return true;\n    }\n\n    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (key === '_owner' && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner.\n        //  _owner contains circular references\n        // and is not needed when comparing the actual elements (and not their owners)\n        continue;\n      }\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var h=require(\"react\"),n=require(\"use-sync-external-store/shim\");function p(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var q=\"function\"===typeof Object.is?Object.is:p,r=n.useSyncExternalStore,t=h.useRef,u=h.useEffect,v=h.useMemo,w=h.useDebugValue;\nexports.useSyncExternalStoreWithSelector=function(a,b,e,l,g){var c=t(null);if(null===c.current){var f={hasValue:!1,value:null};c.current=f}else f=c.current;c=v(function(){function a(a){if(!c){c=!0;d=a;a=l(a);if(void 0!==g&&f.hasValue){var b=f.value;if(g(b,a))return k=b}return k=a}b=k;if(q(d,a))return b;var e=l(a);if(void 0!==g&&g(b,e))return b;d=a;return k=e}var c=!1,d,k,m=void 0===e?null:e;return[function(){return a(b())},null===m?void 0:function(){return a(m())}]},[b,e,l,g]);var d=r(a,c[0],c[1]);\nu(function(){f.hasValue=!0;f.value=d},[d]);w(d);return d};\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var React = require('react');\nvar shim = require('use-sync-external-store/shim');\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction is(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nvar objectIs = typeof Object.is === 'function' ? Object.is : is;\n\nvar useSyncExternalStore = shim.useSyncExternalStore;\n\n// for CommonJS interop.\n\nvar useRef = React.useRef,\n    useEffect = React.useEffect,\n    useMemo = React.useMemo,\n    useDebugValue = React.useDebugValue; // Same as useSyncExternalStore, but supports selector and isEqual arguments.\n\nfunction useSyncExternalStoreWithSelector(subscribe, getSnapshot, getServerSnapshot, selector, isEqual) {\n  // Use this to track the rendered snapshot.\n  var instRef = useRef(null);\n  var inst;\n\n  if (instRef.current === null) {\n    inst = {\n      hasValue: false,\n      value: null\n    };\n    instRef.current = inst;\n  } else {\n    inst = instRef.current;\n  }\n\n  var _useMemo = useMemo(function () {\n    // Track the memoized state using closure variables that are local to this\n    // memoized instance of a getSnapshot function. Intentionally not using a\n    // useRef hook, because that state would be shared across all concurrent\n    // copies of the hook/component.\n    var hasMemo = false;\n    var memoizedSnapshot;\n    var memoizedSelection;\n\n    var memoizedSelector = function (nextSnapshot) {\n      if (!hasMemo) {\n        // The first time the hook is called, there is no memoized result.\n        hasMemo = true;\n        memoizedSnapshot = nextSnapshot;\n\n        var _nextSelection = selector(nextSnapshot);\n\n        if (isEqual !== undefined) {\n          // Even if the selector has changed, the currently rendered selection\n          // may be equal to the new selection. We should attempt to reuse the\n          // current value if possible, to preserve downstream memoizations.\n          if (inst.hasValue) {\n            var currentSelection = inst.value;\n\n            if (isEqual(currentSelection, _nextSelection)) {\n              memoizedSelection = currentSelection;\n              return currentSelection;\n            }\n          }\n        }\n\n        memoizedSelection = _nextSelection;\n        return _nextSelection;\n      } // We may be able to reuse the previous invocation's result.\n\n\n      // We may be able to reuse the previous invocation's result.\n      var prevSnapshot = memoizedSnapshot;\n      var prevSelection = memoizedSelection;\n\n      if (objectIs(prevSnapshot, nextSnapshot)) {\n        // The snapshot is the same as last time. Reuse the previous selection.\n        return prevSelection;\n      } // The snapshot has changed, so we need to compute a new selection.\n\n\n      // The snapshot has changed, so we need to compute a new selection.\n      var nextSelection = selector(nextSnapshot); // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n\n      // If a custom isEqual function is provided, use that to check if the data\n      // has changed. If it hasn't, return the previous selection. That signals\n      // to React that the selections are conceptually equal, and we can bail\n      // out of rendering.\n      if (isEqual !== undefined && isEqual(prevSelection, nextSelection)) {\n        return prevSelection;\n      }\n\n      memoizedSnapshot = nextSnapshot;\n      memoizedSelection = nextSelection;\n      return nextSelection;\n    }; // Assigning this to a constant so that Flow knows it can't change.\n\n\n    // Assigning this to a constant so that Flow knows it can't change.\n    var maybeGetServerSnapshot = getServerSnapshot === undefined ? null : getServerSnapshot;\n\n    var getSnapshotWithSelector = function () {\n      return memoizedSelector(getSnapshot());\n    };\n\n    var getServerSnapshotWithSelector = maybeGetServerSnapshot === null ? undefined : function () {\n      return memoizedSelector(maybeGetServerSnapshot());\n    };\n    return [getSnapshotWithSelector, getServerSnapshotWithSelector];\n  }, [getSnapshot, getServerSnapshot, selector, isEqual]),\n      getSelection = _useMemo[0],\n      getServerSelection = _useMemo[1];\n\n  var value = useSyncExternalStore(subscribe, getSelection, getServerSelection);\n  useEffect(function () {\n    inst.hasValue = true;\n    inst.value = value;\n  }, [value]);\n  useDebugValue(value);\n  return value;\n}\n\nexports.useSyncExternalStoreWithSelector = useSyncExternalStoreWithSelector;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "import type { Editor } from '@tiptap/core'\nimport deepEqual from 'fast-deep-equal/es6/react'\nimport {\n  useDebugValue, useEffect, useLayoutEffect, useState,\n} from 'react'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector'\n\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect\n\nexport type EditorStateSnapshot<TEditor extends Editor | null = Editor | null> = {\n  editor: TEditor;\n  transactionNumber: number;\n};\n\nexport type UseEditorStateOptions<\n  TSelectorResult,\n  TEditor extends Editor | null = Editor | null,\n> = {\n  /**\n   * The editor instance.\n   */\n  editor: TEditor;\n  /**\n   * A selector function to determine the value to compare for re-rendering.\n   */\n  selector: (context: EditorStateSnapshot<TEditor>) => TSelectorResult;\n  /**\n   * A custom equality function to determine if the editor should re-render.\n   * @default `deepEqual` from `fast-deep-equal`\n   */\n  equalityFn?: (a: TSelectorR<PERSON>ult, b: TSelectorResult | null) => boolean;\n};\n\n/**\n * To synchronize the editor instance with the component state,\n * we need to create a separate instance that is not affected by the component re-renders.\n */\nclass EditorStateManager<TEditor extends Editor | null = Editor | null> {\n  private transactionNumber = 0\n\n  private lastTransactionNumber = 0\n\n  private lastSnapshot: EditorStateSnapshot<TEditor>\n\n  private editor: TEditor\n\n  private subscribers = new Set<() => void>()\n\n  constructor(initialEditor: TEditor) {\n    this.editor = initialEditor\n    this.lastSnapshot = { editor: initialEditor, transactionNumber: 0 }\n\n    this.getSnapshot = this.getSnapshot.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.watch = this.watch.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getSnapshot(): EditorStateSnapshot<TEditor> {\n    if (this.transactionNumber === this.lastTransactionNumber) {\n      return this.lastSnapshot\n    }\n    this.lastTransactionNumber = this.transactionNumber\n    this.lastSnapshot = { editor: this.editor, transactionNumber: this.transactionNumber }\n    return this.lastSnapshot\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): EditorStateSnapshot<null> {\n    return { editor: null, transactionNumber: 0 }\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(callback: () => void): () => void {\n    this.subscribers.add(callback)\n    return () => {\n      this.subscribers.delete(callback)\n    }\n  }\n\n  /**\n   * Watch the editor instance for changes.\n   */\n  watch(nextEditor: Editor | null): undefined | (() => void) {\n    this.editor = nextEditor as TEditor\n\n    if (this.editor) {\n      /**\n       * This will force a re-render when the editor state changes.\n       * This is to support things like `editor.can().toggleBold()` in components that `useEditor`.\n       * This could be more efficient, but it's a good trade-off for now.\n       */\n      const fn = () => {\n        this.transactionNumber += 1\n        this.subscribers.forEach(callback => callback())\n      }\n\n      const currentEditor = this.editor\n\n      currentEditor.on('transaction', fn)\n      return () => {\n        currentEditor.off('transaction', fn)\n      }\n    }\n\n    return undefined\n  }\n}\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor>\n): TSelectorResult;\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor | null>\n): TSelectorResult | null;\n\n/**\n * This hook allows you to watch for changes on the editor instance.\n * It will allow you to select a part of the editor state and re-render the component when it changes.\n * @example\n * ```tsx\n * const editor = useEditor({...options})\n * const { currentSelection } = useEditorState({\n *  editor,\n *  selector: snapshot => ({ currentSelection: snapshot.editor.state.selection }),\n * })\n */\nexport function useEditorState<TSelectorResult>(\n  options: UseEditorStateOptions<TSelectorResult, Editor> | UseEditorStateOptions<TSelectorResult, Editor | null>,\n): TSelectorResult | null {\n  const [editorStateManager] = useState(() => new EditorStateManager(options.editor))\n\n  // Using the `useSyncExternalStore` hook to sync the editor instance with the component state\n  const selectedState = useSyncExternalStoreWithSelector(\n    editorStateManager.subscribe,\n    editorStateManager.getSnapshot,\n    editorStateManager.getServerSnapshot,\n    options.selector as UseEditorStateOptions<TSelectorResult, Editor | null>['selector'],\n    options.equalityFn ?? deepEqual,\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    return editorStateManager.watch(options.editor)\n  }, [options.editor, editorStateManager])\n\n  useDebugValue(selectedState)\n\n  return selectedState\n}\n", "import { type EditorOptions, Editor } from '@tiptap/core'\nimport {\n  DependencyList,\n  MutableRefObject,\n  useDebugValue,\n  useEffect,\n  useRef,\n  useState,\n} from 'react'\nimport { useSyncExternalStore } from 'use-sync-external-store/shim'\n\nimport { useEditorState } from './useEditorState.js'\n\nconst isDev = process.env.NODE_ENV !== 'production'\nconst isSSR = typeof window === 'undefined'\nconst isNext = isSSR || Boolean(typeof window !== 'undefined' && (window as any).next)\n\n/**\n * The options for the `useEditor` hook.\n */\nexport type UseEditorOptions = Partial<EditorOptions> & {\n  /**\n   * Whether to render the editor on the first render.\n   * If client-side rendering, set this to `true`.\n   * If server-side rendering, set this to `false`.\n   * @default true\n   */\n  immediatelyRender?: boolean;\n  /**\n   * Whether to re-render the editor on each transaction.\n   * This is legacy behavior that will be removed in future versions.\n   * @default true\n   */\n  shouldRerenderOnTransaction?: boolean;\n};\n\n/**\n * This class handles the creation, destruction, and re-creation of the editor instance.\n */\nclass EditorInstanceManager {\n  /**\n   * The current editor instance.\n   */\n  private editor: Editor | null = null\n\n  /**\n   * The most recent options to apply to the editor.\n   */\n  private options: MutableRefObject<UseEditorOptions>\n\n  /**\n   * The subscriptions to notify when the editor instance\n   * has been created or destroyed.\n   */\n  private subscriptions = new Set<() => void>()\n\n  /**\n   * A timeout to destroy the editor if it was not mounted within a time frame.\n   */\n  private scheduledDestructionTimeout: ReturnType<typeof setTimeout> | undefined\n\n  /**\n   * Whether the editor has been mounted.\n   */\n  private isComponentMounted = false\n\n  /**\n   * The most recent dependencies array.\n   */\n  private previousDeps: DependencyList | null = null\n\n  /**\n   * The unique instance ID. This is used to identify the editor instance. And will be re-generated for each new instance.\n   */\n  public instanceId = ''\n\n  constructor(options: MutableRefObject<UseEditorOptions>) {\n    this.options = options\n    this.subscriptions = new Set<() => void>()\n    this.setEditor(this.getInitialEditor())\n    this.scheduleDestroy()\n\n    this.getEditor = this.getEditor.bind(this)\n    this.getServerSnapshot = this.getServerSnapshot.bind(this)\n    this.subscribe = this.subscribe.bind(this)\n    this.refreshEditorInstance = this.refreshEditorInstance.bind(this)\n    this.scheduleDestroy = this.scheduleDestroy.bind(this)\n    this.onRender = this.onRender.bind(this)\n    this.createEditor = this.createEditor.bind(this)\n  }\n\n  private setEditor(editor: Editor | null) {\n    this.editor = editor\n    this.instanceId = Math.random().toString(36).slice(2, 9)\n\n    // Notify all subscribers that the editor instance has been created\n    this.subscriptions.forEach(cb => cb())\n  }\n\n  private getInitialEditor() {\n    if (this.options.current.immediatelyRender === undefined) {\n      if (isSSR || isNext) {\n        // TODO in the next major release, we should throw an error here\n        if (isDev) {\n          /**\n           * Throw an error in development, to make sure the developer is aware that tiptap cannot be SSR'd\n           * and that they need to set `immediatelyRender` to `false` to avoid hydration mismatches.\n           */\n          console.warn(\n            'Tiptap Error: SSR has been detected, please set `immediatelyRender` explicitly to `false` to avoid hydration mismatches.',\n          )\n        }\n\n        // Best faith effort in production, run the code in the legacy mode to avoid hydration mismatches and errors in production\n        return null\n      }\n\n      // Default to immediately rendering when client-side rendering\n      return this.createEditor()\n    }\n\n    if (this.options.current.immediatelyRender && isSSR && isDev) {\n      // Warn in development, to make sure the developer is aware that tiptap cannot be SSR'd, set `immediatelyRender` to `false` to avoid hydration mismatches.\n      throw new Error(\n        'Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.',\n      )\n    }\n\n    if (this.options.current.immediatelyRender) {\n      return this.createEditor()\n    }\n\n    return null\n  }\n\n  /**\n   * Create a new editor instance. And attach event listeners.\n   */\n  private createEditor(): Editor {\n    const optionsToApply: Partial<EditorOptions> = {\n      ...this.options.current,\n      // Always call the most recent version of the callback function by default\n      onBeforeCreate: (...args) => this.options.current.onBeforeCreate?.(...args),\n      onBlur: (...args) => this.options.current.onBlur?.(...args),\n      onCreate: (...args) => this.options.current.onCreate?.(...args),\n      onDestroy: (...args) => this.options.current.onDestroy?.(...args),\n      onFocus: (...args) => this.options.current.onFocus?.(...args),\n      onSelectionUpdate: (...args) => this.options.current.onSelectionUpdate?.(...args),\n      onTransaction: (...args) => this.options.current.onTransaction?.(...args),\n      onUpdate: (...args) => this.options.current.onUpdate?.(...args),\n      onContentError: (...args) => this.options.current.onContentError?.(...args),\n      onDrop: (...args) => this.options.current.onDrop?.(...args),\n      onPaste: (...args) => this.options.current.onPaste?.(...args),\n    }\n    const editor = new Editor(optionsToApply)\n\n    // no need to keep track of the event listeners, they will be removed when the editor is destroyed\n\n    return editor\n  }\n\n  /**\n   * Get the current editor instance.\n   */\n  getEditor(): Editor | null {\n    return this.editor\n  }\n\n  /**\n   * Always disable the editor on the server-side.\n   */\n  getServerSnapshot(): null {\n    return null\n  }\n\n  /**\n   * Subscribe to the editor instance's changes.\n   */\n  subscribe(onStoreChange: () => void) {\n    this.subscriptions.add(onStoreChange)\n\n    return () => {\n      this.subscriptions.delete(onStoreChange)\n    }\n  }\n\n  static compareOptions(a: UseEditorOptions, b: UseEditorOptions) {\n    return (Object.keys(a) as (keyof UseEditorOptions)[]).every(key => {\n      if (['onCreate', 'onBeforeCreate', 'onDestroy', 'onUpdate', 'onTransaction', 'onFocus', 'onBlur', 'onSelectionUpdate', 'onContentError', 'onDrop', 'onPaste'].includes(key)) {\n        // we don't want to compare callbacks, they are always different and only registered once\n        return true\n      }\n\n      // We often encourage putting extensions inlined in the options object, so we will do a slightly deeper comparison here\n      if (key === 'extensions' && a.extensions && b.extensions) {\n        if (a.extensions.length !== b.extensions.length) {\n          return false\n        }\n        return a.extensions.every((extension, index) => {\n          if (extension !== b.extensions?.[index]) {\n            return false\n          }\n          return true\n        })\n      }\n      if (a[key] !== b[key]) {\n        // if any of the options have changed, we should update the editor options\n        return false\n      }\n      return true\n    })\n  }\n\n  /**\n   * On each render, we will create, update, or destroy the editor instance.\n   * @param deps The dependencies to watch for changes\n   * @returns A cleanup function\n   */\n  onRender(deps: DependencyList) {\n    // The returned callback will run on each render\n    return () => {\n      this.isComponentMounted = true\n      // Cleanup any scheduled destructions, since we are currently rendering\n      clearTimeout(this.scheduledDestructionTimeout)\n\n      if (this.editor && !this.editor.isDestroyed && deps.length === 0) {\n        // if the editor does exist & deps are empty, we don't need to re-initialize the editor generally\n        if (!EditorInstanceManager.compareOptions(this.options.current, this.editor.options)) {\n          // But, the options are different, so we need to update the editor options\n          // Still, this is faster than re-creating the editor\n          this.editor.setOptions({\n            ...this.options.current,\n            editable: this.editor.isEditable,\n          })\n        }\n      } else {\n        // When the editor:\n        // - does not yet exist\n        // - is destroyed\n        // - the deps array changes\n        // We need to destroy the editor instance and re-initialize it\n        this.refreshEditorInstance(deps)\n      }\n\n      return () => {\n        this.isComponentMounted = false\n        this.scheduleDestroy()\n      }\n    }\n  }\n\n  /**\n   * Recreate the editor instance if the dependencies have changed.\n   */\n  private refreshEditorInstance(deps: DependencyList) {\n    if (this.editor && !this.editor.isDestroyed) {\n      // Editor instance already exists\n      if (this.previousDeps === null) {\n        // If lastDeps has not yet been initialized, reuse the current editor instance\n        this.previousDeps = deps\n        return\n      }\n      const depsAreEqual = this.previousDeps.length === deps.length\n        && this.previousDeps.every((dep, index) => dep === deps[index])\n\n      if (depsAreEqual) {\n        // deps exist and are equal, no need to recreate\n        return\n      }\n    }\n\n    if (this.editor && !this.editor.isDestroyed) {\n      // Destroy the editor instance if it exists\n      this.editor.destroy()\n    }\n\n    this.setEditor(this.createEditor())\n\n    // Update the lastDeps to the current deps\n    this.previousDeps = deps\n  }\n\n  /**\n   * Schedule the destruction of the editor instance.\n   * This will only destroy the editor if it was not mounted on the next tick.\n   * This is to avoid destroying the editor instance when it's actually still mounted.\n   */\n  private scheduleDestroy() {\n    const currentInstanceId = this.instanceId\n    const currentEditor = this.editor\n\n    // Wait two ticks to see if the component is still mounted\n    this.scheduledDestructionTimeout = setTimeout(() => {\n      if (this.isComponentMounted && this.instanceId === currentInstanceId) {\n        // If still mounted on the following tick, with the same instanceId, do not destroy the editor\n        if (currentEditor) {\n          // just re-apply options as they might have changed\n          currentEditor.setOptions(this.options.current)\n        }\n        return\n      }\n      if (currentEditor && !currentEditor.isDestroyed) {\n        currentEditor.destroy()\n        if (this.instanceId === currentInstanceId) {\n          this.setEditor(null)\n        }\n      }\n      // This allows the effect to run again between ticks\n      // which may save us from having to re-create the editor\n    }, 1)\n  }\n}\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(\n  options: UseEditorOptions & { immediatelyRender: true },\n  deps?: DependencyList\n): Editor;\n\n/**\n * This hook allows you to create an editor instance.\n * @param options The editor options\n * @param deps The dependencies to watch for changes\n * @returns The editor instance\n * @example const editor = useEditor({ extensions: [...] })\n */\nexport function useEditor(options?: UseEditorOptions, deps?: DependencyList): Editor | null;\n\nexport function useEditor(\n  options: UseEditorOptions = {},\n  deps: DependencyList = [],\n): Editor | null {\n  const mostRecentOptions = useRef(options)\n\n  mostRecentOptions.current = options\n\n  const [instanceManager] = useState(() => new EditorInstanceManager(mostRecentOptions))\n\n  const editor = useSyncExternalStore(\n    instanceManager.subscribe,\n    instanceManager.getEditor,\n    instanceManager.getServerSnapshot,\n  )\n\n  useDebugValue(editor)\n\n  // This effect will handle creating/updating the editor instance\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(instanceManager.onRender(deps))\n\n  // The default behavior is to re-render on each transaction\n  // This is legacy behavior that will be removed in future versions\n  useEditorState({\n    editor,\n    selector: ({ transactionNumber }) => {\n      if (options.shouldRerenderOnTransaction === false) {\n        // This will prevent the editor from re-rendering on each transaction\n        return null\n      }\n\n      // This will avoid re-rendering on the first transaction when `immediatelyRender` is set to `true`\n      if (options.immediatelyRender && transactionNumber === 0) {\n        return 0\n      }\n      return transactionNumber + 1\n    },\n  })\n\n  return editor\n}\n", "import { Editor } from '@tiptap/core'\nimport React, {\n  createContext, HTMLAttributes, ReactNode, useContext,\n} from 'react'\n\nimport { EditorContent } from './EditorContent.js'\nimport { useEditor, UseEditorOptions } from './useEditor.js'\n\nexport type EditorContextValue = {\n  editor: Editor | null;\n}\n\nexport const EditorContext = createContext<EditorContextValue>({\n  editor: null,\n})\n\nexport const EditorConsumer = EditorContext.Consumer\n\n/**\n * A hook to get the current editor instance.\n */\nexport const useCurrentEditor = () => useContext(EditorContext)\n\nexport type EditorProviderProps = {\n  children?: ReactNode;\n  slotBefore?: ReactNode;\n  slotAfter?: ReactNode;\n  editorContainerProps?: HTMLAttributes<HTMLDivElement>;\n} & UseEditorOptions\n\n/**\n * This is the provider component for the editor.\n * It allows the editor to be accessible across the entire component tree\n * with `useCurrentEditor`.\n */\nexport function EditorProvider({\n  children, slotAfter, slotBefore, editorContainerProps = {}, ...editorOptions\n}: EditorProviderProps) {\n  const editor = useEditor(editorOptions)\n\n  if (!editor) {\n    return null\n  }\n\n  return (\n    <EditorContext.Provider value={{ editor }}>\n      {slotBefore}\n      <EditorConsumer>\n        {({ editor: currentEditor }) => (\n          <EditorContent editor={currentEditor} {...editorContainerProps} />\n        )}\n      </EditorConsumer>\n      {children}\n      {slotAfter}\n    </EditorContext.Provider>\n  )\n}\n", "import { BubbleMenuPlugin, BubbleMenuPluginProps } from '@tiptap/extension-bubble-menu'\nimport React, { useEffect, useState } from 'react'\n\nimport { useCurrentEditor } from './Context.js'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;\n\nexport type BubbleMenuProps = Omit<Optional<BubbleMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: BubbleMenuPluginProps['editor'] | null;\n  className?: string;\n  children: React.ReactNode;\n  updateDelay?: number;\n};\n\nexport const BubbleMenu = (props: BubbleMenuProps) => {\n  const [element, setElement] = useState<HTMLDivElement | null>(null)\n  const { editor: currentEditor } = useCurrentEditor()\n\n  useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    if (props.editor?.isDestroyed || currentEditor?.isDestroyed) {\n      return\n    }\n\n    const {\n      pluginKey = 'bubbleMenu', editor, tippyOptions = {}, updateDelay, shouldShow = null,\n    } = props\n\n    const menuEditor = editor || currentEditor\n\n    if (!menuEditor) {\n      console.warn('BubbleMenu component is not rendered inside of an editor component or does not have editor prop.')\n      return\n    }\n\n    const plugin = BubbleMenuPlugin({\n      updateDelay,\n      editor: menuEditor,\n      element,\n      pluginKey,\n      shouldShow,\n      tippyOptions,\n    })\n\n    menuEditor.registerPlugin(plugin)\n    return () => { menuEditor.unregisterPlugin(pluginKey) }\n  }, [props.editor, currentEditor, element])\n\n  return (\n    <div ref={setElement} className={props.className} style={{ visibility: 'hidden' }}>\n      {props.children}\n    </div>\n  )\n}\n", "import { FloatingMenuPlugin, FloatingMenuPluginProps } from '@tiptap/extension-floating-menu'\nimport React, {\n  useEffect, useState,\n} from 'react'\n\nimport { useCurrentEditor } from './Context.js'\n\ntype Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>\n\nexport type FloatingMenuProps = Omit<Optional<FloatingMenuPluginProps, 'pluginKey'>, 'element' | 'editor'> & {\n  editor: FloatingMenuPluginProps['editor'] | null;\n  className?: string,\n  children: React.ReactNode\n}\n\nexport const FloatingMenu = (props: FloatingMenuProps) => {\n  const [element, setElement] = useState<HTMLDivElement | null>(null)\n  const { editor: currentEditor } = useCurrentEditor()\n\n  useEffect(() => {\n    if (!element) {\n      return\n    }\n\n    if (props.editor?.isDestroyed || currentEditor?.isDestroyed) {\n      return\n    }\n\n    const {\n      pluginKey = 'floatingMenu',\n      editor,\n      tippyOptions = {},\n      shouldShow = null,\n    } = props\n\n    const menuEditor = editor || currentEditor\n\n    if (!menuEditor) {\n      console.warn('FloatingMenu component is not rendered inside of an editor component or does not have editor prop.')\n      return\n    }\n\n    const plugin = FloatingMenuPlugin({\n      pluginKey,\n      editor: menuEditor,\n      element,\n      tippyOptions,\n      shouldShow,\n    })\n\n    menuEditor.registerPlugin(plugin)\n    return () => { menuEditor.unregisterPlugin(pluginKey) }\n  }, [\n    props.editor,\n    currentEditor,\n    element,\n  ])\n\n  return (\n    <div ref={setElement} className={props.className} style={{ visibility: 'hidden' }}>\n      {props.children}\n    </div>\n  )\n}\n", "import { createContext, useContext } from 'react'\n\nexport interface ReactNodeViewContextProps {\n  onDragStart: (event: DragEvent) => void,\n  nodeViewContentRef: (element: HTMLElement | null) => void,\n}\n\nexport const ReactNodeViewContext = createContext<Partial<ReactNodeViewContextProps>>({\n  onDragStart: undefined,\n})\n\nexport const useReactNodeView = () => useContext(ReactNodeViewContext)\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewContentProps {\n  [key: string]: any,\n  as?: React.ElementType,\n}\n\nexport const NodeViewContent: React.FC<NodeViewContentProps> = props => {\n  const Tag = props.as || 'div'\n  const { nodeViewContentRef } = useReactNodeView()\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={nodeViewContentRef}\n      data-node-view-content=\"\"\n      style={{\n        whiteSpace: 'pre-wrap',\n        ...props.style,\n      }}\n    />\n  )\n}\n", "import React from 'react'\n\nimport { useReactNodeView } from './useReactNodeView.js'\n\nexport interface NodeViewWrapperProps {\n  [key: string]: any,\n  as?: React.ElementType,\n}\n\nexport const NodeViewWrapper: React.FC<NodeViewWrapperProps> = React.forwardRef((props, ref) => {\n  const { onDragStart } = useReactNodeView()\n  const Tag = props.as || 'div'\n\n  return (\n    // @ts-ignore\n    <Tag\n      {...props}\n      ref={ref}\n      data-node-view-wrapper=\"\"\n      onDragStart={onDragStart}\n      style={{\n        whiteSpace: 'normal',\n        ...props.style,\n      }}\n    />\n  )\n})\n", "import type { Editor } from '@tiptap/core'\nimport type {\n  ComponentClass,\n  ForwardRefExoticComponent,\n  FunctionComponent,\n  PropsWithoutRef,\n  ReactNode,\n  RefAttributes,\n} from 'react'\nimport React, { version as reactVersion } from 'react'\nimport { flushSync } from 'react-dom'\n\nimport { EditorWithContentComponent } from './Editor.js'\n\n/**\n * Check if a component is a class component.\n * @param Component\n * @returns {boolean}\n */\nfunction isClassComponent(Component: any) {\n  return !!(\n    typeof Component === 'function'\n    && Component.prototype\n    && Component.prototype.isReactComponent\n  )\n}\n\n/**\n * Check if a component is a forward ref component.\n * @param Component\n * @returns {boolean}\n */\nfunction isForwardRefComponent(Component: any) {\n  return !!(\n    typeof Component === 'object'\n    && Component.$$typeof?.toString() === 'Symbol(react.forward_ref)'\n  )\n}\n\n/**\n * Check if we're running React 19+ by detecting if function components support ref props\n * @returns {boolean}\n */\nfunction isReact19Plus(): boolean {\n  // React 19 is detected by checking React version if available\n  // In practice, we'll use a more conservative approach and assume React 18 behavior\n  // unless we can definitively detect React 19\n  try {\n    // @ts-ignore\n    if (reactVersion) {\n      const majorVersion = parseInt(reactVersion.split('.')[0], 10)\n\n      return majorVersion >= 19\n    }\n  } catch {\n    // Fallback to React 18 behavior if we can't determine version\n  }\n  return false\n}\n\nexport interface ReactRendererOptions {\n  /**\n   * The editor instance.\n   * @type {Editor}\n   */\n  editor: Editor,\n\n  /**\n   * The props for the component.\n   * @type {Record<string, any>}\n   * @default {}\n   */\n  props?: Record<string, any>,\n\n  /**\n   * The tag name of the element.\n   * @type {string}\n   * @default 'div'\n   */\n  as?: string,\n\n  /**\n   * The class name of the element.\n   * @type {string}\n   * @default ''\n   * @example 'foo bar'\n   */\n  className?: string,\n}\n\ntype ComponentType<R, P> =\n  | ComponentClass<P>\n  | FunctionComponent<P>\n  | ForwardRefExoticComponent<PropsWithoutRef<P> & RefAttributes<R>>\n\n/**\n * The ReactRenderer class. It's responsible for rendering React components inside the editor.\n * @example\n * new ReactRenderer(MyComponent, {\n *   editor,\n *   props: {\n *     foo: 'bar',\n *   },\n *   as: 'span',\n * })\n*/\nexport class ReactRenderer<R = unknown, P extends Record<string, any> = object> {\n  id: string\n\n  editor: Editor\n\n  component: any\n\n  element: Element\n\n  props: P\n\n  reactElement: ReactNode\n\n  ref: R | null = null\n\n  /**\n   * Immediately creates element and renders the provided React component.\n   */\n  constructor(component: ComponentType<R, P>, {\n    editor,\n    props = {},\n    as = 'div',\n    className = '',\n  }: ReactRendererOptions) {\n    this.id = Math.floor(Math.random() * 0xFFFFFFFF).toString()\n    this.component = component\n    this.editor = editor as EditorWithContentComponent\n    this.props = props as P\n    this.element = document.createElement(as)\n    this.element.classList.add('react-renderer')\n\n    if (className) {\n      this.element.classList.add(...className.split(' '))\n    }\n\n    if (this.editor.isInitialized) {\n      // On first render, we need to flush the render synchronously\n      // Renders afterwards can be async, but this fixes a cursor positioning issue\n      flushSync(() => {\n        this.render()\n      })\n    } else {\n      this.render()\n    }\n  }\n\n  /**\n   * Render the React component.\n   */\n  render(): void {\n    const Component = this.component\n    const props = this.props\n    const editor = this.editor as EditorWithContentComponent\n\n    // Handle ref forwarding with React 18/19 compatibility\n    const isReact19 = isReact19Plus()\n    const isClassComp = isClassComponent(Component)\n    const isForwardRefComp = isForwardRefComponent(Component)\n\n    const elementProps = { ...props }\n\n    if (!elementProps.ref) {\n      if (isReact19) {\n        // React 19: ref is a standard prop for all components\n        // @ts-ignore - Setting ref prop for React 19 compatibility\n        elementProps.ref = (ref: R) => {\n          this.ref = ref\n        }\n      } else if (isClassComp || isForwardRefComp) {\n        // React 18 and prior: only set ref for class components and forwardRef components\n        // @ts-ignore - Setting ref prop for React 18 class/forwardRef components\n        elementProps.ref = (ref: R) => {\n          this.ref = ref\n        }\n      }\n      // For function components in React 18, we can't use ref - the component won't receive it\n      // This is a limitation we have to accept for React 18 function components without forwardRef\n    }\n\n    this.reactElement = <Component {...elementProps} />\n\n    editor?.contentComponent?.setRenderer(this.id, this)\n  }\n\n  /**\n   * Re-renders the React component with new props.\n   */\n  updateProps(props: Record<string, any> = {}): void {\n    this.props = {\n      ...this.props,\n      ...props,\n    }\n\n    this.render()\n  }\n\n  /**\n   * Destroy the React component.\n   */\n  destroy(): void {\n    const editor = this.editor as EditorWithContentComponent\n\n    editor?.contentComponent?.removeRenderer(this.id)\n  }\n\n  /**\n   * Update the attributes of the element that holds the React component.\n   */\n  updateAttributes(attributes: Record<string, string>): void {\n    Object.keys(attributes).forEach(key => {\n      this.element.setAttribute(key, attributes[key])\n    })\n  }\n}\n", "import type {\n  DecorationWithType, Editor, NodeViewRenderer, NodeViewRendererOptions,\n} from '@tiptap/core'\nimport { getRenderedAttributes, NodeView } from '@tiptap/core'\nimport type { Node, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport type { Decoration, DecorationSource, NodeView as ProseMirrorNodeView } from '@tiptap/pm/view'\nimport type { ComponentType, NamedExoticComponent } from 'react'\nimport React, { createElement, createRef, memo } from 'react'\n\nimport { EditorWithContentComponent } from './Editor.js'\nimport { ReactRenderer } from './ReactRenderer.js'\nimport type { ReactNodeViewProps } from './types.js'\nimport type { ReactNodeViewContextProps } from './useReactNodeView.js'\nimport { ReactNodeViewContext } from './useReactNodeView.js'\n\nexport interface ReactNodeViewRendererOptions extends NodeViewRendererOptions {\n  /**\n   * This function is called when the node view is updated.\n   * It allows you to compare the old node with the new node and decide if the component should update.\n   */\n  update:\n    | ((props: {\n        oldNode: ProseMirrorNode;\n        oldDecorations: readonly Decoration[];\n        oldInnerDecorations: DecorationSource;\n        newNode: ProseMirrorNode;\n        newDecorations: readonly Decoration[];\n        innerDecorations: DecorationSource;\n        updateProps: () => void;\n      }) => boolean)\n    | null;\n  /**\n   * The tag name of the element wrapping the React component.\n   */\n  as?: string;\n  /**\n   * The class name of the element wrapping the React component.\n   */\n  className?: string;\n  /**\n   * Attributes that should be applied to the element wrapping the React component.\n   * If this is a function, it will be called each time the node view is updated.\n   * If this is an object, it will be applied once when the node view is mounted.\n   */\n  attrs?:\n    | Record<string, string>\n    | ((props: {\n        node: ProseMirrorNode;\n        HTMLAttributes: Record<string, any>;\n      }) => Record<string, string>);\n}\n\nexport class ReactNodeView<\n  T = HTMLElement,\n  Component extends ComponentType<ReactNodeViewProps<T>> = ComponentType<ReactNodeViewProps<T>>,\n  NodeEditor extends Editor = Editor,\n  Options extends ReactNodeViewRendererOptions = ReactNodeViewRendererOptions,\n> extends NodeView<Component, NodeEditor, Options> {\n  /**\n   * The renderer instance.\n   */\n  renderer!: ReactRenderer<unknown, ReactNodeViewProps<T>>\n\n  /**\n   * The element that holds the rich-text content of the node.\n   */\n  contentDOMElement!: HTMLElement | null\n\n  /**\n   * Setup the React component.\n   * Called on initialization.\n   */\n  mount() {\n    const props = {\n      editor: this.editor,\n      node: this.node,\n      decorations: this.decorations as DecorationWithType[],\n      innerDecorations: this.innerDecorations,\n      view: this.view,\n      selected: false,\n      extension: this.extension,\n      HTMLAttributes: this.HTMLAttributes,\n      getPos: () => this.getPos(),\n      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n      deleteNode: () => this.deleteNode(),\n      ref: createRef<T>(),\n    } satisfies ReactNodeViewProps<T>\n\n    if (!(this.component as any).displayName) {\n      const capitalizeFirstChar = (string: string): string => {\n        return string.charAt(0).toUpperCase() + string.substring(1)\n      }\n\n      this.component.displayName = capitalizeFirstChar(this.extension.name)\n    }\n\n    const onDragStart = this.onDragStart.bind(this)\n    const nodeViewContentRef: ReactNodeViewContextProps['nodeViewContentRef'] = element => {\n      if (element && this.contentDOMElement && element.firstChild !== this.contentDOMElement) {\n        element.appendChild(this.contentDOMElement)\n      }\n    }\n    const context = { onDragStart, nodeViewContentRef }\n    const Component = this.component\n    // For performance reasons, we memoize the provider component\n    // And all of the things it requires are declared outside of the component, so it doesn't need to re-render\n    const ReactNodeViewProvider: NamedExoticComponent<ReactNodeViewProps<T>> = memo(componentProps => {\n      return (\n        <ReactNodeViewContext.Provider value={context}>\n          {createElement(Component, componentProps)}\n        </ReactNodeViewContext.Provider>\n      )\n    })\n\n    ReactNodeViewProvider.displayName = 'ReactNodeView'\n\n    if (this.node.isLeaf) {\n      this.contentDOMElement = null\n    } else if (this.options.contentDOMElementTag) {\n      this.contentDOMElement = document.createElement(this.options.contentDOMElementTag)\n    } else {\n      this.contentDOMElement = document.createElement(this.node.isInline ? 'span' : 'div')\n    }\n\n    if (this.contentDOMElement) {\n      this.contentDOMElement.dataset.nodeViewContentReact = ''\n      // For some reason the whiteSpace prop is not inherited properly in Chrome and Safari\n      // With this fix it seems to work fine\n      // See: https://github.com/ueberdosis/tiptap/issues/1197\n      this.contentDOMElement.style.whiteSpace = 'inherit'\n    }\n\n    let as = this.node.isInline ? 'span' : 'div'\n\n    if (this.options.as) {\n      as = this.options.as\n    }\n\n    const { className = '' } = this.options\n\n    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this)\n\n    this.renderer = new ReactRenderer(ReactNodeViewProvider, {\n      editor: this.editor,\n      props,\n      as,\n      className: `node-${this.node.type.name} ${className}`.trim(),\n    })\n\n    this.editor.on('selectionUpdate', this.handleSelectionUpdate)\n    this.updateElementAttributes()\n  }\n\n  /**\n   * Return the DOM element.\n   * This is the element that will be used to display the node view.\n   */\n  get dom() {\n    if (\n      this.renderer.element.firstElementChild\n      && !this.renderer.element.firstElementChild?.hasAttribute('data-node-view-wrapper')\n    ) {\n      throw Error('Please use the NodeViewWrapper component for your node view.')\n    }\n\n    return this.renderer.element as HTMLElement\n  }\n\n  /**\n   * Return the content DOM element.\n   * This is the element that will be used to display the rich-text content of the node.\n   */\n  get contentDOM() {\n    if (this.node.isLeaf) {\n      return null\n    }\n\n    return this.contentDOMElement\n  }\n\n  /**\n   * On editor selection update, check if the node is selected.\n   * If it is, call `selectNode`, otherwise call `deselectNode`.\n   */\n  handleSelectionUpdate() {\n    const { from, to } = this.editor.state.selection\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n\n    if (from <= pos && to >= pos + this.node.nodeSize) {\n      if (this.renderer.props.selected) {\n        return\n      }\n\n      this.selectNode()\n    } else {\n      if (!this.renderer.props.selected) {\n        return\n      }\n\n      this.deselectNode()\n    }\n  }\n\n  /**\n   * On update, update the React component.\n   * To prevent unnecessary updates, the `update` option can be used.\n   */\n  update(\n    node: Node,\n    decorations: readonly Decoration[],\n    innerDecorations: DecorationSource,\n  ): boolean {\n    const rerenderComponent = (props?: Record<string, any>) => {\n      this.renderer.updateProps(props)\n      if (typeof this.options.attrs === 'function') {\n        this.updateElementAttributes()\n      }\n    }\n\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    if (typeof this.options.update === 'function') {\n      const oldNode = this.node\n      const oldDecorations = this.decorations\n      const oldInnerDecorations = this.innerDecorations\n\n      this.node = node\n      this.decorations = decorations\n      this.innerDecorations = innerDecorations\n\n      return this.options.update({\n        oldNode,\n        oldDecorations,\n        newNode: node,\n        newDecorations: decorations,\n        oldInnerDecorations,\n        innerDecorations,\n        updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n      })\n    }\n\n    if (\n      node === this.node\n      && this.decorations === decorations\n      && this.innerDecorations === innerDecorations\n    ) {\n      return true\n    }\n\n    this.node = node\n    this.decorations = decorations\n    this.innerDecorations = innerDecorations\n\n    rerenderComponent({ node, decorations, innerDecorations })\n\n    return true\n  }\n\n  /**\n   * Select the node.\n   * Add the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  selectNode() {\n    this.renderer.updateProps({\n      selected: true,\n    })\n    this.renderer.element.classList.add('ProseMirror-selectednode')\n  }\n\n  /**\n   * Deselect the node.\n   * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  deselectNode() {\n    this.renderer.updateProps({\n      selected: false,\n    })\n    this.renderer.element.classList.remove('ProseMirror-selectednode')\n  }\n\n  /**\n   * Destroy the React component instance.\n   */\n  destroy() {\n    this.renderer.destroy()\n    this.editor.off('selectionUpdate', this.handleSelectionUpdate)\n    this.contentDOMElement = null\n  }\n\n  /**\n   * Update the attributes of the top-level element that holds the React component.\n   * Applying the attributes defined in the `attrs` option.\n   */\n  updateElementAttributes() {\n    if (this.options.attrs) {\n      let attrsObj: Record<string, string> = {}\n\n      if (typeof this.options.attrs === 'function') {\n        const extensionAttributes = this.editor.extensionManager.attributes\n        const HTMLAttributes = getRenderedAttributes(this.node, extensionAttributes)\n\n        attrsObj = this.options.attrs({ node: this.node, HTMLAttributes })\n      } else {\n        attrsObj = this.options.attrs\n      }\n\n      this.renderer.updateAttributes(attrsObj)\n    }\n  }\n}\n\n/**\n * Create a React node view renderer.\n */\nexport function ReactNodeViewRenderer<T = HTMLElement>(\n  component: ComponentType<ReactNodeViewProps<T>>,\n  options?: Partial<ReactNodeViewRendererOptions>,\n): NodeViewRenderer {\n  return props => {\n    // try to get the parent component\n    // this is important for vue devtools to show the component hierarchy correctly\n    // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n    if (!(props.editor as EditorWithContentComponent).contentComponent) {\n      return {} as unknown as ProseMirrorNodeView\n    }\n\n    return new ReactNodeView<T>(component, props, options)\n  }\n}\n"], "names": ["require$$0", "React", "shimModule", "require$$1", "useSyncExternalStore", "forwardRef", "withSelectorModule", "useLayoutEffect", "useEffect", "useState", "useSyncExternalStoreWithSelector", "useDebugValue", "Editor", "useRef", "createContext", "useContext", "BubbleMenuPlugin", "FloatingMenuPlugin", "reactVersion", "flushSync", "NodeView", "createRef", "memo", "createElement", "getRenderedAttributes"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;CASa,IAAI,CAAC,CAACA,KAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,GAAG,OAAO,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;EAClc,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,WAAW,GAAG,OAAO,MAAM,EAAE,WAAW,GAAG,OAAO,MAAM,CAAC,QAAQ,EAAE,WAAW,GAAG,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,uCAA4B,CAAA,oBAAA,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;CCE3U,CAAA,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;CAC3C,GAAE,CAAC,WAAW;;CAId;CACA,CAAA;IACE,OAAO,8BAA8B,KAAK,WAAW;IACrD,OAAO,8BAA8B,CAAC,2BAA2B;CACnE,KAAI;IACF;IACA,8BAA8B,CAAC,2BAA2B,CAAC,IAAI,KAAK,EAAE,CAAC;;CAEzE,WAAU,IAAIC,OAAK,GAAGD,KAAgB;;CAEtC,CAAA,IAAI,oBAAoB,GAAGC,OAAK,CAAC,kDAAkD;;EAEnF,SAAS,KAAK,CAAC,MAAM,EAAE;IACrB;MACE;CACJ,OAAM,KAAK,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;UACjH,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;;;QAGpC,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC;;;;;CAKzC,CAAA,SAAS,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE;CAC3C;CACA;IACE;CACF,KAAI,IAAI,sBAAsB,GAAG,oBAAoB,CAAC,sBAAsB;CAC5E,KAAI,IAAI,KAAK,GAAG,sBAAsB,CAAC,gBAAgB,EAAE;;CAEzD,KAAI,IAAI,KAAK,KAAK,EAAE,EAAE;QAChB,MAAM,IAAI,IAAI;QACd,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC;OAC5B;;;MAGD,IAAI,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;CAClD,OAAM,OAAO,MAAM,CAAC,IAAI,CAAC;CACzB,MAAK,CAAC,CAAC;;MAEH,cAAc,CAAC,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,CAAC;CACjD;CACA;;CAEA,KAAI,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,cAAc,CAAC;;;;CAI1E;CACA;CACA;CACA;CACA,CAAA,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAChB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;;;CAItE,CAAA,IAAI,QAAQ,GAAG,OAAO,MAAM,CAAC,EAAE,KAAK,UAAU,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE;;CAE/D;;CAEA,CAAA,IAAI,QAAQ,GAAGA,OAAK,CAAC,QAAQ;CAC7B,KAAI,SAAS,GAAGA,OAAK,CAAC,SAAS;CAC/B,KAAI,eAAe,GAAGA,OAAK,CAAC,eAAe;CAC3C,KAAI,aAAa,GAAGA,OAAK,CAAC,aAAa;EACvC,IAAI,iBAAiB,GAAG,KAAK;EAC7B,IAAI,0BAA0B,GAAG,KAAK,CAAC;CACvC;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAEA,CAAA,SAAS,oBAAoB,CAAC,SAAS,EAAE,WAAW;CACpD;CACA;CACA;CACA,CAAA,iBAAiB,EAAE;IACjB;MACE,IAAI,CAAC,iBAAiB,EAAE;CAC5B,OAAM,IAAIA,OAAK,CAAC,eAAe,KAAK,SAAS,EAAE;UACvC,iBAAiB,GAAG,IAAI;;UAExB,KAAK,CAAC,gEAAgE,GAAG,6CAA6C,GAAG,gEAAgE,GAAG,yBAAyB,CAAC;;;KAG3N;CACH;CACA;CACA;;;CAGA,GAAE,IAAI,KAAK,GAAG,WAAW,EAAE;;IAEzB;MACE,IAAI,CAAC,0BAA0B,EAAE;CACrC,OAAM,IAAI,WAAW,GAAG,WAAW,EAAE;;QAE/B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE;CACzC,SAAQ,KAAK,CAAC,sEAAsE,CAAC;;UAE7E,0BAA0B,GAAG,IAAI;;;KAGtC;CACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;;CAGA,GAAE,IAAI,SAAS,GAAG,QAAQ,CAAC;CAC3B,KAAI,IAAI,EAAE;QACJ,KAAK,EAAE,KAAK;QACZ,WAAW,EAAE;;CAEnB,IAAG,CAAC;CACJ,OAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI;CAC9B,OAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;CACjC;CACA;;;IAGE,eAAe,CAAC,YAAY;CAC9B,KAAI,IAAI,CAAC,KAAK,GAAG,KAAK;CACtB,KAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;CACnC;CACA;CACA;;CAEA,KAAI,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;CACtC;CACA,OAAM,WAAW,CAAC;UACV,IAAI,EAAE;CACd,QAAO,CAAC;;KAEL,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;IACnC,SAAS,CAAC,YAAY;CACxB;CACA;CACA,KAAI,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;CACtC;CACA,OAAM,WAAW,CAAC;UACV,IAAI,EAAE;CACd,QAAO,CAAC;;;MAGJ,IAAI,iBAAiB,GAAG,YAAY;CACxC;CACA;CACA;CACA;CACA;CACA;CACA,OAAM,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;CACxC;CACA,SAAQ,WAAW,CAAC;YACV,IAAI,EAAE;CAChB,UAAS,CAAC;;CAEV,MAAK,CAAC;;;CAGN,KAAI,OAAO,SAAS,CAAC,iBAAiB,CAAC;CACvC,IAAG,EAAE,CAAC,SAAS,CAAC,CAAC;CACjB,GAAE,aAAa,CAAC,KAAK,CAAC;IACpB,OAAO,KAAK;;;EAGd,SAAS,sBAAsB,CAAC,IAAI,EAAE;CACtC,GAAE,IAAI,iBAAiB,GAAG,IAAI,CAAC,WAAW;CAC1C,GAAE,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK;;CAE5B,GAAE,IAAI;CACN,KAAI,IAAI,SAAS,GAAG,iBAAiB,EAAE;MACnC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC;KACvC,CAAC,OAAO,KAAK,EAAE;MACd,OAAO,IAAI;;;;CAIf,CAAA,SAAS,sBAAsB,CAAC,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE;CAC3E;CACA;CACA;CACA;IACE,OAAO,WAAW,EAAE;;;EAGtB,IAAI,SAAS,GAAG,CAAC,EAAE,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,KAAK,WAAW,CAAC;;CAEnJ,CAAA,IAAI,mBAAmB,GAAG,CAAC,SAAS;;CAEpC,CAAA,IAAI,IAAI,GAAG,mBAAmB,GAAG,sBAAsB,GAAG,oBAAoB;CAC9E,CAAA,IAAI,sBAAsB,GAAGA,OAAK,CAAC,oBAAoB,KAAK,SAAS,GAAGA,OAAK,CAAC,oBAAoB,GAAG,IAAI;;CAEzG,CAA4B,oCAAA,CAAA,oBAAA,GAAG,sBAAsB;CACrD;CACA,CAAA;IACE,OAAO,8BAA8B,KAAK,WAAW;IACrD,OAAO,8BAA8B,CAAC,0BAA0B;CAClE,KAAI;IACF;IACA,8BAA8B,CAAC,0BAA0B,CAAC,IAAI,KAAK,EAAE,CAAC;;CAExE;CACA,IAAG,GAAG;CACN;;;;CC5OA,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;CAC3C,EAAEC,IAAA,CAAA,OAAc,GAAGF,8CAAA,EAAgE;CACnF,CAAC,MAAM;CACP,EAAEE,IAAA,CAAA,OAAc,GAAGC,2CAAA,EAA6D;CAChF;;;;CCIA,MAAM,SAAS,GAAG,CAChB,GAAG,IAA2D,KAC5D;KACF,OAAO,CAAC,IAAO,KAAI;CACjB,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,IAAG;CACjB,YAAA,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;iBAC7B,GAAG,CAAC,IAAI,CAAC;;kBACJ,IAAI,GAAG,EAAE;CACb,gBAAA,GAAkC,CAAC,OAAO,GAAG,IAAI;;CAEtD,SAAC,CAAC;CACJ,KAAC;CACH,CAAC;CAED;;CAEG;CACH,MAAM,OAAO,GAAqD,CAAC,EACjE,gBAAgB,GACjB,KAAI;;CAEH,IAAA,MAAM,SAAS,GAAGC,gCAAoB,CACpC,gBAAgB,CAAC,SAAS,EAC1B,gBAAgB,CAAC,WAAW,EAC5B,gBAAgB,CAAC,iBAAiB,CACnC;;KAGD,QACE,KACG,CAAA,aAAA,CAAA,KAAA,CAAA,QAAA,EAAA,IAAA,EAAA,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CACxB;CAEP,CAAC;CAOD,SAAS,WAAW,GAAA;CAClB,IAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAc;KACzC,IAAI,SAAS,GAAsC,EAAE;KAErD,OAAO;CACL;;CAEG;CACH,QAAA,SAAS,CAAC,QAAoB,EAAA;CAC5B,YAAA,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC;CACzB,YAAA,OAAO,MAAK;CACV,gBAAA,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;CAC9B,aAAC;UACF;SACD,WAAW,GAAA;CACT,YAAA,OAAO,SAAS;UACjB;SACD,iBAAiB,GAAA;CACf,YAAA,OAAO,SAAS;UACjB;CACD;;CAEG;SACH,WAAW,CAAC,EAAU,EAAE,QAAuB,EAAA;CAC7C,YAAA,SAAS,GAAG;CACV,gBAAA,GAAG,SAAS;CACZ,gBAAA,CAAC,EAAE,GAAG,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;cACzE;aAED,WAAW,CAAC,OAAO,CAAC,UAAU,IAAI,UAAU,EAAE,CAAC;UAChD;CACD;;CAEG;CACH,QAAA,cAAc,CAAC,EAAU,EAAA;CACvB,YAAA,MAAM,aAAa,GAAG,EAAE,GAAG,SAAS,EAAE;CAEtC,YAAA,OAAO,aAAa,CAAC,EAAE,CAAC;aACxB,SAAS,GAAG,aAAa;aACzB,WAAW,CAAC,OAAO,CAAC,UAAU,IAAI,UAAU,EAAE,CAAC;UAChD;MACF;CACH;CAEa,MAAA,iBAAkB,SAAQ,KAAK,CAAC,SAG5C,CAAA;CAOC,IAAA,WAAA,CAAY,KAAyB,EAAA;;SACnC,KAAK,CAAC,KAAK,CAAC;CACZ,QAAA,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC,SAAS,EAAE;CACzC,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;SAExB,IAAI,CAAC,KAAK,GAAG;aACX,8BAA8B,EAAE,OAAO,CAAC,CAAA,EAAA,GAAC,KAAK,CAAC,MAA4C,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,gBAAgB,CAAC;UAC/G;;KAGH,iBAAiB,GAAA;SACf,IAAI,CAAC,IAAI,EAAE;;KAGb,kBAAkB,GAAA;SAChB,IAAI,CAAC,IAAI,EAAE;;KAGb,IAAI,GAAA;CACF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAA2C;CAErE,QAAA,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;CAC3D,YAAA,IAAI,MAAM,CAAC,gBAAgB,EAAE;iBAC3B;;CAGF,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO;CAE7C,YAAA,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;aAEpD,MAAM,CAAC,UAAU,CAAC;iBAChB,OAAO;CACR,aAAA,CAAC;CAEF,YAAA,MAAM,CAAC,gBAAgB,GAAG,WAAW,EAAE;;CAGvC,YAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,8BAA8B,EAAE;;iBAE9C,IAAI,CAAC,6BAA6B,GAAG,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAK;CAC1E,oBAAA,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAG;CACxB,wBAAA,IAAI,CAAC,SAAS,CAAC,8BAA8B,EAAE;6BAC7C,OAAO;CACL,gCAAA,8BAA8B,EAAE,IAAI;8BACrC;;CAEH,wBAAA,OAAO,SAAS;CAClB,qBAAC,CAAC;;CAGF,oBAAA,IAAI,IAAI,CAAC,6BAA6B,EAAE;yBACtC,IAAI,CAAC,6BAA6B,EAAE;;CAExC,iBAAC,CAAC;;aAGJ,MAAM,CAAC,eAAe,EAAE;CAExB,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;;KAI3B,oBAAoB,GAAA;CAClB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAA2C;SAErE,IAAI,CAAC,MAAM,EAAE;aACX;;CAGF,QAAA,IAAI,CAAC,WAAW,GAAG,KAAK;CAExB,QAAA,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;CACvB,YAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;CACnB,gBAAA,SAAS,EAAE,EAAE;CACd,aAAA,CAAC;;CAGJ,QAAA,IAAI,IAAI,CAAC,6BAA6B,EAAE;aACtC,IAAI,CAAC,6BAA6B,EAAE;;CAGtC,QAAA,MAAM,CAAC,gBAAgB,GAAG,IAAI;SAE9B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE;aACtC;;SAGF,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;CAEhD,QAAA,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;SAEvD,MAAM,CAAC,UAAU,CAAC;CAChB,YAAA,OAAO,EAAE,UAAU;CACpB,SAAA,CAAC;;KAGJ,MAAM,GAAA;CACJ,QAAA,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK;CAEhD,QAAA,QACE,KAAA,CAAA,aAAA,CAAA,KAAA,CAAA,QAAA,EAAA,IAAA;CACE,YAAA,KAAA,CAAA,aAAA,CAAA,KAAA,EAAA,EAAK,GAAG,EAAE,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAM,GAAA,IAAI,EAAI,CAAA;CAEjE,YAAA,CAAA,MAAM,KAAN,IAAA,IAAA,MAAM,uBAAN,MAAM,CAAE,gBAAgB,KAAI,KAAA,CAAA,aAAA,CAAC,OAAO,EAAC,EAAA,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,EAAI,CAAA,CAClF;;CAGR;CAED;CACA,MAAM,oBAAoB,GAAGC,gBAAU,CACrC,CAAC,KAA2C,EAAE,GAAG,KAAI;CACnD,IAAA,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,MAAK;CAC7B,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,QAAQ,EAAE;;CAE1D,KAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;;CAGlB,IAAA,OAAO,KAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE;SAC5C,GAAG;CACH,QAAA,QAAQ,EAAE,GAAG;CACb,QAAA,GAAG,KAAK;CACT,KAAA,CAAC;CACJ,CAAC,CACF;AAEY,OAAA,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,oBAAoB;;CC9N5D,IAAA,KAAc,GAAG,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;CACtC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI;;CAE1B,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,QAAQ,EAAE;CAC9D,IAAI,IAAI,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,WAAW,EAAE,OAAO,KAAK;;CAErD,IAAI,IAAI,MAAM,EAAE,CAAC,EAAE,IAAI;CACvB,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;CAC1B,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;CACvB,MAAM,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK;CAC1C,MAAM,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC;CAChC,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;CAC5C,MAAM,OAAO,IAAI;CACjB;;;CAGA,IAAI,IAAI,CAAC,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,CAAC,EAAE;CAClD,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,KAAK;CACzC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE;CAC3B,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;CACtC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE;CAC3B,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;CACnD,MAAM,OAAO,IAAI;CACjB;;CAEA,IAAI,IAAI,CAAC,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,CAAC,EAAE;CAClD,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,OAAO,KAAK;CACzC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE;CAC3B,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;CACtC,MAAM,OAAO,IAAI;CACjB;;CAEA,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;CACxD,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;CACvB,MAAM,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK;CAC1C,MAAM,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC;CAChC,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;CACvC,MAAM,OAAO,IAAI;CACjB;;;CAGA,IAAI,IAAI,CAAC,CAAC,WAAW,KAAK,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK;CACrF,IAAI,IAAI,CAAC,CAAC,OAAO,KAAK,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,OAAO,EAAE;CAClF,IAAI,IAAI,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE;;CAEtF,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;CACzB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM;CACxB,IAAI,IAAI,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK;;CAEtD,IAAI,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC;CAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;;CAEzE,IAAI,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;CACjC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;;CAEvB,MAAM,IAAI,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE;CAC1C;CACA;CACA;CACA,QAAQ;CACR;;CAEA,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,KAAK;CAC9C;;CAEA,IAAI,OAAO,IAAI;CACf;;CAEA;CACA,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;CACvB,CAAC;;;;;;;;;;;;;;;;;;;;;;;CCrEY,IAAI,CAAC,CAACL,KAAgB,CAAC,CAAC,CAACG,WAAuC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,GAAG,OAAO,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa;CAC5Q,CAAA,2BAAA,CAAA,gCAAwC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACtf,CAAA,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;CCCzD,CAAA,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;CAC3C,GAAE,CAAC,WAAW;;CAId;CACA,CAAA;IACE,OAAO,8BAA8B,KAAK,WAAW;IACrD,OAAO,8BAA8B,CAAC,2BAA2B;CACnE,KAAI;IACF;IACA,8BAA8B,CAAC,2BAA2B,CAAC,IAAI,KAAK,EAAE,CAAC;;CAEzE,WAAU,IAAIF,OAAK,GAAGD,KAAgB;EACtC,IAAI,IAAI,GAAGG,WAAuC;;CAElD;CACA;CACA;CACA;CACA,CAAA,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAChB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;;;CAItE,CAAA,IAAI,QAAQ,GAAG,OAAO,MAAM,CAAC,EAAE,KAAK,UAAU,GAAG,MAAM,CAAC,EAAE,GAAG,EAAE;;CAE/D,CAAA,IAAI,oBAAoB,GAAG,IAAI,CAAC,oBAAoB;;CAEpD;;CAEA,CAAA,IAAI,MAAM,GAAGF,OAAK,CAAC,MAAM;CACzB,KAAI,SAAS,GAAGA,OAAK,CAAC,SAAS;CAC/B,KAAI,OAAO,GAAGA,OAAK,CAAC,OAAO;CAC3B,KAAI,aAAa,GAAGA,OAAK,CAAC,aAAa,CAAC;;EAExC,SAAS,gCAAgC,CAAC,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,OAAO,EAAE;CACxG;CACA,GAAE,IAAI,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC;IAC1B,IAAI,IAAI;;CAEV,GAAE,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;CAChC,KAAI,IAAI,GAAG;QACL,QAAQ,EAAE,KAAK;QACf,KAAK,EAAE;CACb,MAAK;CACL,KAAI,OAAO,CAAC,OAAO,GAAG,IAAI;CAC1B,IAAG,MAAM;CACT,KAAI,IAAI,GAAG,OAAO,CAAC,OAAO;;;CAG1B,GAAE,IAAI,QAAQ,GAAG,OAAO,CAAC,YAAY;CACrC;CACA;CACA;CACA;CACA,KAAI,IAAI,OAAO,GAAG,KAAK;MACnB,IAAI,gBAAgB;MACpB,IAAI,iBAAiB;;CAEzB,KAAI,IAAI,gBAAgB,GAAG,UAAU,YAAY,EAAE;QAC7C,IAAI,CAAC,OAAO,EAAE;CACpB;UACQ,OAAO,GAAG,IAAI;UACd,gBAAgB,GAAG,YAAY;;CAEvC,SAAQ,IAAI,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC;;CAEnD,SAAQ,IAAI,OAAO,KAAK,SAAS,EAAE;CACnC;CACA;CACA;CACA,WAAU,IAAI,IAAI,CAAC,QAAQ,EAAE;CAC7B,aAAY,IAAI,gBAAgB,GAAG,IAAI,CAAC,KAAK;;CAE7C,aAAY,IAAI,OAAO,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE;gBAC7C,iBAAiB,GAAG,gBAAgB;gBACpC,OAAO,gBAAgB;;;;;UAK7B,iBAAiB,GAAG,cAAc;UAClC,OAAO,cAAc;SACtB;;;CAGP;CACA,OAAM,IAAI,YAAY,GAAG,gBAAgB;CACzC,OAAM,IAAI,aAAa,GAAG,iBAAiB;;CAE3C,OAAM,IAAI,QAAQ,CAAC,YAAY,EAAE,YAAY,CAAC,EAAE;CAChD;UACQ,OAAO,aAAa;SACrB;;;CAGP;CACA,OAAM,IAAI,aAAa,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;CACjD;CACA;CACA;;CAEA;CACA;CACA;CACA;QACM,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE;UAClE,OAAO,aAAa;;;QAGtB,gBAAgB,GAAG,YAAY;QAC/B,iBAAiB,GAAG,aAAa;QACjC,OAAO,aAAa;CAC1B,MAAK,CAAC;;;CAGN;MACI,IAAI,sBAAsB,GAAG,iBAAiB,KAAK,SAAS,GAAG,IAAI,GAAG,iBAAiB;;MAEvF,IAAI,uBAAuB,GAAG,YAAY;CAC9C,OAAM,OAAO,gBAAgB,CAAC,WAAW,EAAE,CAAC;CAC5C,MAAK;;MAED,IAAI,6BAA6B,GAAG,sBAAsB,KAAK,IAAI,GAAG,SAAS,GAAG,YAAY;CAClG,OAAM,OAAO,gBAAgB,CAAC,sBAAsB,EAAE,CAAC;CACvD,MAAK;CACL,KAAI,OAAO,CAAC,uBAAuB,EAAE,6BAA6B,CAAC;KAChE,EAAE,CAAC,WAAW,EAAE,iBAAiB,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;CACzD,OAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC;CAChC,OAAM,kBAAkB,GAAG,QAAQ,CAAC,CAAC,CAAC;;IAEpC,IAAI,KAAK,GAAG,oBAAoB,CAAC,SAAS,EAAE,YAAY,EAAE,kBAAkB,CAAC;IAC7E,SAAS,CAAC,YAAY;CACxB,KAAI,IAAI,CAAC,QAAQ,GAAG,IAAI;CACxB,KAAI,IAAI,CAAC,KAAK,GAAG,KAAK;CACtB,IAAG,EAAE,CAAC,KAAK,CAAC,CAAC;CACb,GAAE,aAAa,CAAC,KAAK,CAAC;IACpB,OAAO,KAAK;;;CAGd,CAAwC,wBAAA,CAAA,gCAAA,GAAG,gCAAgC;CAC3E;CACA,CAAA;IACE,OAAO,8BAA8B,KAAK,WAAW;IACrD,OAAO,8BAA8B,CAAC,0BAA0B;CAClE,KAAI;IACF;IACA,8BAA8B,CAAC,0BAA0B,CAAC,IAAI,KAAK,EAAE,CAAC;;CAExE;CACA,IAAG,GAAG;CACN;;;;CClKA,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;CAC3C,EAAEK,YAAA,CAAA,OAAc,GAAGN,kCAAA,EAA8E;CACjG,CAAC,MAAM;CACP,EAAEM,YAAA,CAAA,OAAc,GAAGH,+BAAA,EAA2E;CAC9F;;;;CCCA,MAAM,yBAAyB,GAAG,OAAO,MAAM,KAAK,WAAW,GAAGI,qBAAe,GAAGC,eAAS;CA0B7F;;;CAGG;CACH,MAAM,kBAAkB,CAAA;CAWtB,IAAA,WAAA,CAAY,aAAsB,EAAA;SAV1B,IAAiB,CAAA,iBAAA,GAAG,CAAC;SAErB,IAAqB,CAAA,qBAAA,GAAG,CAAC;CAMzB,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,GAAG,EAAc;CAGzC,QAAA,IAAI,CAAC,MAAM,GAAG,aAAa;CAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,iBAAiB,EAAE,CAAC,EAAE;SAEnE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;SAC9C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;SAC1D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;SAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;CAG5C;;CAEG;KACH,WAAW,GAAA;SACT,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,qBAAqB,EAAE;aACzD,OAAO,IAAI,CAAC,YAAY;;CAE1B,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB;CACnD,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EAAE;SACtF,OAAO,IAAI,CAAC,YAAY;;CAG1B;;CAEG;KACH,iBAAiB,GAAA;SACf,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,EAAE;;CAG/C;;CAEG;CACH,IAAA,SAAS,CAAC,QAAoB,EAAA;CAC5B,QAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC;CAC9B,QAAA,OAAO,MAAK;CACV,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;CACnC,SAAC;;CAGH;;CAEG;CACH,IAAA,KAAK,CAAC,UAAyB,EAAA;CAC7B,QAAA,IAAI,CAAC,MAAM,GAAG,UAAqB;CAEnC,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;CACf;;;;CAIG;aACH,MAAM,EAAE,GAAG,MAAK;CACd,gBAAA,IAAI,CAAC,iBAAiB,IAAI,CAAC;CAC3B,gBAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,EAAE,CAAC;CAClD,aAAC;CAED,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM;CAEjC,YAAA,aAAa,CAAC,EAAE,CAAC,aAAa,EAAE,EAAE,CAAC;CACnC,YAAA,OAAO,MAAK;CACV,gBAAA,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC;CACtC,aAAC;;CAGH,QAAA,OAAO,SAAS;;CAEnB;CA+BD;;;;;;;;;;CAUG;CACG,SAAU,cAAc,CAC5B,OAA+G,EAAA;;CAE/G,IAAA,MAAM,CAAC,kBAAkB,CAAC,GAAGC,cAAQ,CAAC,MAAM,IAAI,kBAAkB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;KAGnF,MAAM,aAAa,GAAGC,oDAAgC,CACpD,kBAAkB,CAAC,SAAS,EAC5B,kBAAkB,CAAC,WAAW,EAC9B,kBAAkB,CAAC,iBAAiB,EACpC,OAAO,CAAC,QAA6E,EACrF,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,SAAS,CAChC;KAED,yBAAyB,CAAC,MAAK;SAC7B,OAAO,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;MAChD,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;KAExCC,mBAAa,CAAC,aAAa,CAAC;CAE5B,IAAA,OAAO,aAAa;CACtB;;CCpKA,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;CACnD,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,WAAW;CAC3C,MAAM,MAAM,GAAG,KAAK,IAAI,OAAO,CAAC,OAAO,MAAM,KAAK,WAAW,IAAK,MAAc,CAAC,IAAI,CAAC;CAqBtF;;CAEG;CACH,MAAM,qBAAqB,CAAA;CAqCzB,IAAA,WAAA,CAAY,OAA2C,EAAA;CApCvD;;CAEG;SACK,IAAM,CAAA,MAAA,GAAkB,IAAI;CAOpC;;;CAGG;CACK,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,GAAG,EAAc;CAO7C;;CAEG;SACK,IAAkB,CAAA,kBAAA,GAAG,KAAK;CAElC;;CAEG;SACK,IAAY,CAAA,YAAA,GAA0B,IAAI;CAElD;;CAEG;SACI,IAAU,CAAA,UAAA,GAAG,EAAE;CAGpB,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;CACtB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAc;SAC1C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACvC,IAAI,CAAC,eAAe,EAAE;SAEtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;SAC1C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;SAC1D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;SAC1C,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;SAClE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;SACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;SACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;;CAG1C,IAAA,SAAS,CAAC,MAAqB,EAAA;CACrC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;CACpB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;CAGxD,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;;KAGhC,gBAAgB,GAAA;SACtB,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE;CACxD,YAAA,IAAI,KAAK,IAAI,MAAM,EAAE;;iBAEnB,IAAI,KAAK,EAAE;CACT;;;CAGG;CACH,oBAAA,OAAO,CAAC,IAAI,CACV,0HAA0H,CAC3H;;;CAIH,gBAAA,OAAO,IAAI;;;CAIb,YAAA,OAAO,IAAI,CAAC,YAAY,EAAE;;CAG5B,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,IAAI,KAAK,IAAI,KAAK,EAAE;;CAE5D,YAAA,MAAM,IAAI,KAAK,CACb,kOAAkO,CACnO;;SAGH,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE;CAC1C,YAAA,OAAO,IAAI,CAAC,YAAY,EAAE;;CAG5B,QAAA,OAAO,IAAI;;CAGb;;CAEG;KACK,YAAY,GAAA;CAClB,QAAA,MAAM,cAAc,GAA2B;CAC7C,YAAA,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;;aAEvB,cAAc,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,cAAc,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;aAC3E,MAAM,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,MAAM,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;aAC3D,QAAQ,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,QAAQ,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;aAC/D,SAAS,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,SAAS,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;aACjE,OAAO,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,OAAO,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;aAC7D,iBAAiB,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,iBAAiB,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;aACjF,aAAa,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,aAAa,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;aACzE,QAAQ,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,QAAQ,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;aAC/D,cAAc,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,cAAc,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;aAC3E,MAAM,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,MAAM,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;aAC3D,OAAO,EAAE,CAAC,GAAG,IAAI,KAAI,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,CAAC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAC,OAAO,mDAAG,GAAG,IAAI,CAAC,CAAA,EAAA;UAC9D;CACD,QAAA,MAAM,MAAM,GAAG,IAAIC,WAAM,CAAC,cAAc,CAAC;;CAIzC,QAAA,OAAO,MAAM;;CAGf;;CAEG;KACH,SAAS,GAAA;SACP,OAAO,IAAI,CAAC,MAAM;;CAGpB;;CAEG;KACH,iBAAiB,GAAA;CACf,QAAA,OAAO,IAAI;;CAGb;;CAEG;CACH,IAAA,SAAS,CAAC,aAAyB,EAAA;CACjC,QAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;CAErC,QAAA,OAAO,MAAK;CACV,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC;CAC1C,SAAC;;CAGH,IAAA,OAAO,cAAc,CAAC,CAAmB,EAAE,CAAmB,EAAA;SAC5D,OAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,CAAgC,CAAC,KAAK,CAAC,GAAG,IAAG;CAChE,YAAA,IAAI,CAAC,UAAU,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,QAAQ,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;;CAE3K,gBAAA,OAAO,IAAI;;;CAIb,YAAA,IAAI,GAAG,KAAK,YAAY,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,UAAU,EAAE;CACxD,gBAAA,IAAI,CAAC,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE;CAC/C,oBAAA,OAAO,KAAK;;iBAEd,OAAO,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,KAAK,KAAI;;qBAC7C,IAAI,SAAS,MAAK,CAAA,EAAA,GAAA,CAAC,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,KAAK,CAAC,CAAA,EAAE;CACvC,wBAAA,OAAO,KAAK;;CAEd,oBAAA,OAAO,IAAI;CACb,iBAAC,CAAC;;aAEJ,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;;CAErB,gBAAA,OAAO,KAAK;;CAEd,YAAA,OAAO,IAAI;CACb,SAAC,CAAC;;CAGJ;;;;CAIG;CACH,IAAA,QAAQ,CAAC,IAAoB,EAAA;;CAE3B,QAAA,OAAO,MAAK;CACV,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;;CAE9B,YAAA,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC;CAE9C,YAAA,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;;CAEhE,gBAAA,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;;;CAGpF,oBAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;CACrB,wBAAA,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO;CACvB,wBAAA,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;CACjC,qBAAA,CAAC;;;kBAEC;;;;;;CAML,gBAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;;CAGlC,YAAA,OAAO,MAAK;CACV,gBAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;iBAC/B,IAAI,CAAC,eAAe,EAAE;CACxB,aAAC;CACH,SAAC;;CAGH;;CAEG;CACK,IAAA,qBAAqB,CAAC,IAAoB,EAAA;SAChD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;;CAE3C,YAAA,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE;;CAE9B,gBAAA,IAAI,CAAC,YAAY,GAAG,IAAI;iBACxB;;aAEF,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC;CAClD,mBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;aAEjE,IAAI,YAAY,EAAE;;iBAEhB;;;SAIJ,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;;CAE3C,YAAA,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;;SAGvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;;CAGnC,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;CAG1B;;;;CAIG;KACK,eAAe,GAAA;CACrB,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU;CACzC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM;;CAGjC,QAAA,IAAI,CAAC,2BAA2B,GAAG,UAAU,CAAC,MAAK;aACjD,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,EAAE;;iBAEpE,IAAI,aAAa,EAAE;;qBAEjB,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;;iBAEhD;;CAEF,YAAA,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;iBAC/C,aAAa,CAAC,OAAO,EAAE;CACvB,gBAAA,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAiB,EAAE;CACzC,oBAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;;;;UAKzB,EAAE,CAAC,CAAC;;CAER;UAuBe,SAAS,CACvB,UAA4B,EAAE,EAC9B,OAAuB,EAAE,EAAA;CAEzB,IAAA,MAAM,iBAAiB,GAAGC,YAAM,CAAC,OAAO,CAAC;CAEzC,IAAA,iBAAiB,CAAC,OAAO,GAAG,OAAO;CAEnC,IAAA,MAAM,CAAC,eAAe,CAAC,GAAGJ,cAAQ,CAAC,MAAM,IAAI,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;CAEtF,IAAA,MAAM,MAAM,GAAGL,gCAAoB,CACjC,eAAe,CAAC,SAAS,EACzB,eAAe,CAAC,SAAS,EACzB,eAAe,CAAC,iBAAiB,CAClC;KAEDO,mBAAa,CAAC,MAAM,CAAC;;;KAIrBH,eAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;;CAIzC,IAAA,cAAc,CAAC;SACb,MAAM;CACN,QAAA,QAAQ,EAAE,CAAC,EAAE,iBAAiB,EAAE,KAAI;CAClC,YAAA,IAAI,OAAO,CAAC,2BAA2B,KAAK,KAAK,EAAE;;CAEjD,gBAAA,OAAO,IAAI;;;aAIb,IAAI,OAAO,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,CAAC,EAAE;CACxD,gBAAA,OAAO,CAAC;;aAEV,OAAO,iBAAiB,GAAG,CAAC;UAC7B;CACF,KAAA,CAAC;CAEF,IAAA,OAAO,MAAM;CACf;;AC3WO,OAAM,aAAa,GAAGM,mBAAa,CAAqB;CAC7D,IAAA,MAAM,EAAE,IAAI;CACb,CAAA;AAEY,OAAA,cAAc,GAAG,aAAa,CAAC;CAE5C;;CAEG;AACU,OAAA,gBAAgB,GAAG,MAAMC,gBAAU,CAAC,aAAa;CAS9D;;;;CAIG;UACa,cAAc,CAAC,EAC7B,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,oBAAoB,GAAG,EAAE,EAAE,GAAG,aAAa,EACxD,EAAA;CACpB,IAAA,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa,CAAC;KAEvC,IAAI,CAAC,MAAM,EAAE;CACX,QAAA,OAAO,IAAI;;KAGb,QACE,KAAC,CAAA,aAAA,CAAA,aAAa,CAAC,QAAQ,IAAC,KAAK,EAAE,EAAE,MAAM,EAAE,EAAA;SACtC,UAAU;SACX,KAAC,CAAA,aAAA,CAAA,cAAc,QACZ,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,MACzB,KAAC,CAAA,aAAA,CAAA,aAAa,IAAC,MAAM,EAAE,aAAa,EAAM,GAAA,oBAAoB,EAAI,CAAA,CACnE,CACc;SAChB,QAAQ;SACR,SAAS,CACa;CAE7B;;AC1Ca,OAAA,UAAU,GAAG,CAAC,KAAsB,KAAI;KACnD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAGN,cAAQ,CAAwB,IAAI,CAAC;KACnE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,gBAAgB,EAAE;KAEpDD,eAAS,CAAC,MAAK;;SACb,IAAI,CAAC,OAAO,EAAE;aACZ;;CAGF,QAAA,IAAI,CAAA,CAAA,EAAA,GAAA,KAAK,CAAC,MAAM,0CAAE,WAAW,MAAI,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,WAAW,CAAA,EAAE;aAC3D;;CAGF,QAAA,MAAM,EACJ,SAAS,GAAG,YAAY,EAAE,MAAM,EAAE,YAAY,GAAG,EAAE,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI,GACpF,GAAG,KAAK;CAET,QAAA,MAAM,UAAU,GAAG,MAAM,IAAI,aAAa;SAE1C,IAAI,CAAC,UAAU,EAAE;CACf,YAAA,OAAO,CAAC,IAAI,CAAC,kGAAkG,CAAC;aAChH;;SAGF,MAAM,MAAM,GAAGQ,oCAAgB,CAAC;aAC9B,WAAW;CACX,YAAA,MAAM,EAAE,UAAU;aAClB,OAAO;aACP,SAAS;aACT,UAAU;aACV,YAAY;CACb,SAAA,CAAC;CAEF,QAAA,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC;CACjC,QAAA,OAAO,MAAQ,EAAA,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA,EAAE;MACxD,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;KAE1C,QACE,KAAK,CAAA,aAAA,CAAA,KAAA,EAAA,EAAA,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,EAC9E,EAAA,KAAK,CAAC,QAAQ,CACX;CAEV;;ACzCa,OAAA,YAAY,GAAG,CAAC,KAAwB,KAAI;KACvD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAGP,cAAQ,CAAwB,IAAI,CAAC;KACnE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,gBAAgB,EAAE;KAEpDD,eAAS,CAAC,MAAK;;SACb,IAAI,CAAC,OAAO,EAAE;aACZ;;CAGF,QAAA,IAAI,CAAA,CAAA,EAAA,GAAA,KAAK,CAAC,MAAM,0CAAE,WAAW,MAAI,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,WAAW,CAAA,EAAE;aAC3D;;CAGF,QAAA,MAAM,EACJ,SAAS,GAAG,cAAc,EAC1B,MAAM,EACN,YAAY,GAAG,EAAE,EACjB,UAAU,GAAG,IAAI,GAClB,GAAG,KAAK;CAET,QAAA,MAAM,UAAU,GAAG,MAAM,IAAI,aAAa;SAE1C,IAAI,CAAC,UAAU,EAAE;CACf,YAAA,OAAO,CAAC,IAAI,CAAC,oGAAoG,CAAC;aAClH;;SAGF,MAAM,MAAM,GAAGS,wCAAkB,CAAC;aAChC,SAAS;CACT,YAAA,MAAM,EAAE,UAAU;aAClB,OAAO;aACP,YAAY;aACZ,UAAU;CACX,SAAA,CAAC;CAEF,QAAA,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC;CACjC,QAAA,OAAO,MAAQ,EAAA,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA,EAAE;CACzD,KAAC,EAAE;CACD,QAAA,KAAK,CAAC,MAAM;SACZ,aAAa;SACb,OAAO;CACR,KAAA,CAAC;KAEF,QACE,KAAK,CAAA,aAAA,CAAA,KAAA,EAAA,EAAA,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,EAC9E,EAAA,KAAK,CAAC,QAAQ,CACX;CAEV;;ACxDO,OAAM,oBAAoB,GAAGH,mBAAa,CAAqC;CACpF,IAAA,WAAW,EAAE,SAAS;CACvB,CAAA;AAEY,OAAA,gBAAgB,GAAG,MAAMC,gBAAU,CAAC,oBAAoB;;ACFxD,OAAA,eAAe,GAAmC,KAAK,IAAG;CACrE,IAAA,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK;CAC7B,IAAA,MAAM,EAAE,kBAAkB,EAAE,GAAG,gBAAgB,EAAE;KAEjD;;KAEE,KAAC,CAAA,aAAA,CAAA,GAAG,EACE,EAAA,GAAA,KAAK,EACT,GAAG,EAAE,kBAAkB,EACA,wBAAA,EAAA,EAAE,EACzB,KAAK,EAAE;CACL,YAAA,UAAU,EAAE,UAAU;aACtB,GAAG,KAAK,CAAC,KAAK;CACf,SAAA,EAAA,CACD;CAEN;;AChBO,OAAM,eAAe,GAAmC,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,GAAG,KAAI;CAC7F,IAAA,MAAM,EAAE,WAAW,EAAE,GAAG,gBAAgB,EAAE;CAC1C,IAAA,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK;KAE7B;;CAEE,IAAA,KAAA,CAAA,aAAA,CAAC,GAAG,EAAA,EAAA,GACE,KAAK,EACT,GAAG,EAAE,GAAG,EACe,wBAAA,EAAA,EAAE,EACzB,WAAW,EAAE,WAAW,EACxB,KAAK,EAAE;CACL,YAAA,UAAU,EAAE,QAAQ;aACpB,GAAG,KAAK,CAAC,KAAK;CACf,SAAA,EAAA,CACD;CAEN,CAAC;;CCZD;;;;CAIG;CACH,SAAS,gBAAgB,CAAC,SAAc,EAAA;CACtC,IAAA,OAAO,CAAC,EACN,OAAO,SAAS,KAAK;CAClB,WAAA,SAAS,CAAC;CACV,WAAA,SAAS,CAAC,SAAS,CAAC,gBAAgB,CACxC;CACH;CAEA;;;;CAIG;CACH,SAAS,qBAAqB,CAAC,SAAc,EAAA;;CAC3C,IAAA,OAAO,CAAC,EACN,OAAO,SAAS,KAAK;YAClB,CAAA,CAAA,EAAA,GAAA,SAAS,CAAC,QAAQ,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,QAAQ,EAAE,MAAK,2BAA2B,CAClE;CACH;CAEA;;;CAGG;CACH,SAAS,aAAa,GAAA;;;;CAIpB,IAAA,IAAI;;SAEF,IAAIG,aAAY,EAAE;CAChB,YAAA,MAAM,YAAY,GAAG,QAAQ,CAACA,aAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;aAE7D,OAAO,YAAY,IAAI,EAAE;;;CAE3B,IAAA,MAAM;;;CAGR,IAAA,OAAO,KAAK;CACd;CAqCA;;;;;;;;;;CAUE;OACW,aAAa,CAAA;CAexB;;CAEG;CACH,IAAA,WAAA,CAAY,SAA8B,EAAE,EAC1C,MAAM,EACN,KAAK,GAAG,EAAE,EACV,EAAE,GAAG,KAAK,EACV,SAAS,GAAG,EAAE,GACO,EAAA;SAVvB,IAAG,CAAA,GAAA,GAAa,IAAI;CAWlB,QAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,QAAQ,EAAE;CAC3D,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS;CAC1B,QAAA,IAAI,CAAC,MAAM,GAAG,MAAoC;CAClD,QAAA,IAAI,CAAC,KAAK,GAAG,KAAU;SACvB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;SACzC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;SAE5C,IAAI,SAAS,EAAE;CACb,YAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;CAGrD,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;;;aAG7BC,kBAAS,CAAC,MAAK;iBACb,IAAI,CAAC,MAAM,EAAE;CACf,aAAC,CAAC;;cACG;aACL,IAAI,CAAC,MAAM,EAAE;;;CAIjB;;CAEG;KACH,MAAM,GAAA;;CACJ,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;CAChC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;CACxB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAoC;;CAGxD,QAAA,MAAM,SAAS,GAAG,aAAa,EAAE;CACjC,QAAA,MAAM,WAAW,GAAG,gBAAgB,CAAC,SAAS,CAAC;CAC/C,QAAA,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,SAAS,CAAC;CAEzD,QAAA,MAAM,YAAY,GAAG,EAAE,GAAG,KAAK,EAAE;CAEjC,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE;aACrB,IAAI,SAAS,EAAE;;;CAGb,gBAAA,YAAY,CAAC,GAAG,GAAG,CAAC,GAAM,KAAI;CAC5B,oBAAA,IAAI,CAAC,GAAG,GAAG,GAAG;CAChB,iBAAC;;CACI,iBAAA,IAAI,WAAW,IAAI,gBAAgB,EAAE;;;CAG1C,gBAAA,YAAY,CAAC,GAAG,GAAG,CAAC,GAAM,KAAI;CAC5B,oBAAA,IAAI,CAAC,GAAG,GAAG,GAAG;CAChB,iBAAC;;;;;SAML,IAAI,CAAC,YAAY,GAAG,KAAA,CAAA,aAAA,CAAC,SAAS,EAAK,EAAA,GAAA,YAAY,GAAI;CAEnD,QAAA,CAAA,EAAA,GAAA,MAAM,KAAN,IAAA,IAAA,MAAM,KAAN,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,MAAM,CAAE,gBAAgB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;;CAGtD;;CAEG;KACH,WAAW,CAAC,QAA6B,EAAE,EAAA;SACzC,IAAI,CAAC,KAAK,GAAG;aACX,GAAG,IAAI,CAAC,KAAK;CACb,YAAA,GAAG,KAAK;UACT;SAED,IAAI,CAAC,MAAM,EAAE;;CAGf;;CAEG;KACH,OAAO,GAAA;;CACL,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAoC;CAExD,QAAA,CAAA,EAAA,GAAA,MAAM,KAAA,IAAA,IAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;;CAGnD;;CAEG;CACH,IAAA,gBAAgB,CAAC,UAAkC,EAAA;SACjD,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,GAAG,IAAG;CACpC,YAAA,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;CACjD,SAAC,CAAC;;CAEL;;CCvKK,MAAO,aAKX,SAAQC,aAAwC,CAAA;CAWhD;;;CAGG;KACH,KAAK,GAAA;CACH,QAAA,MAAM,KAAK,GAAG;aACZ,MAAM,EAAE,IAAI,CAAC,MAAM;aACnB,IAAI,EAAE,IAAI,CAAC,IAAI;aACf,WAAW,EAAE,IAAI,CAAC,WAAmC;aACrD,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;aACvC,IAAI,EAAE,IAAI,CAAC,IAAI;CACf,YAAA,QAAQ,EAAE,KAAK;aACf,SAAS,EAAE,IAAI,CAAC,SAAS;aACzB,cAAc,EAAE,IAAI,CAAC,cAAc;CACnC,YAAA,MAAM,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE;CAC3B,YAAA,gBAAgB,EAAE,CAAC,UAAU,GAAG,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;CACxE,YAAA,UAAU,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE;aACnC,GAAG,EAAEC,eAAS,EAAK;UACY;CAEjC,QAAA,IAAI,CAAE,IAAI,CAAC,SAAiB,CAAC,WAAW,EAAE;CACxC,YAAA,MAAM,mBAAmB,GAAG,CAAC,MAAc,KAAY;CACrD,gBAAA,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;CAC7D,aAAC;CAED,YAAA,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;;SAGvE,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;CAC/C,QAAA,MAAM,kBAAkB,GAAoD,OAAO,IAAG;CACpF,YAAA,IAAI,OAAO,IAAI,IAAI,CAAC,iBAAiB,IAAI,OAAO,CAAC,UAAU,KAAK,IAAI,CAAC,iBAAiB,EAAE;CACtF,gBAAA,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;;CAE/C,SAAC;CACD,QAAA,MAAM,OAAO,GAAG,EAAE,WAAW,EAAE,kBAAkB,EAAE;CACnD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;;;CAGhC,QAAA,MAAM,qBAAqB,GAAgDC,UAAI,CAAC,cAAc,IAAG;CAC/F,YAAA,QACE,KAAC,CAAA,aAAA,CAAA,oBAAoB,CAAC,QAAQ,EAAA,EAAC,KAAK,EAAE,OAAO,EAC1C,EAAAC,mBAAa,CAAC,SAAS,EAAE,cAAc,CAAC,CACX;CAEpC,SAAC,CAAC;CAEF,QAAA,qBAAqB,CAAC,WAAW,GAAG,eAAe;CAEnD,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;CACpB,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;;CACxB,aAAA,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;CAC5C,YAAA,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;;cAC7E;aACL,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,KAAK,CAAC;;CAGtF,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE;aAC1B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,oBAAoB,GAAG,EAAE;;;;aAIxD,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS;;CAGrD,QAAA,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,KAAK;CAE5C,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;CACnB,YAAA,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE;;SAGtB,MAAM,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,OAAO;SAEvC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;CAElE,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,aAAa,CAAC,qBAAqB,EAAE;aACvD,MAAM,EAAE,IAAI,CAAC,MAAM;aACnB,KAAK;aACL,EAAE;CACF,YAAA,SAAS,EAAE,CAAA,KAAA,EAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAI,CAAA,EAAA,SAAS,CAAE,CAAA,CAAC,IAAI,EAAE;CAC7D,SAAA,CAAC;SAEF,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,CAAC;SAC7D,IAAI,CAAC,uBAAuB,EAAE;;CAGhC;;;CAGG;CACH,IAAA,IAAI,GAAG,GAAA;;CACL,QAAA,IACE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;CACnB,eAAA,EAAC,CAAA,EAAA,GAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,YAAY,CAAC,wBAAwB,CAAC,CAAA,EACnF;CACA,YAAA,MAAM,KAAK,CAAC,8DAA8D,CAAC;;CAG7E,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAsB;;CAG7C;;;CAGG;CACH,IAAA,IAAI,UAAU,GAAA;CACZ,QAAA,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;CACpB,YAAA,OAAO,IAAI;;SAGb,OAAO,IAAI,CAAC,iBAAiB;;CAG/B;;;CAGG;KACH,qBAAqB,GAAA;CACnB,QAAA,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS;CAChD,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE;CAEzB,QAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;aAC3B;;CAGF,QAAA,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;aACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE;iBAChC;;aAGF,IAAI,CAAC,UAAU,EAAE;;cACZ;aACL,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE;iBACjC;;aAGF,IAAI,CAAC,YAAY,EAAE;;;CAIvB;;;CAGG;CACH,IAAA,MAAM,CACJ,IAAU,EACV,WAAkC,EAClC,gBAAkC,EAAA;CAElC,QAAA,MAAM,iBAAiB,GAAG,CAAC,KAA2B,KAAI;CACxD,YAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;aAChC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;iBAC5C,IAAI,CAAC,uBAAuB,EAAE;;CAElC,SAAC;SAED,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;CAChC,YAAA,OAAO,KAAK;;SAGd,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,UAAU,EAAE;CAC7C,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI;CACzB,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW;CACvC,YAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,gBAAgB;CAEjD,YAAA,IAAI,CAAC,IAAI,GAAG,IAAI;CAChB,YAAA,IAAI,CAAC,WAAW,GAAG,WAAW;CAC9B,YAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;CAExC,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;iBACzB,OAAO;iBACP,cAAc;CACd,gBAAA,OAAO,EAAE,IAAI;CACb,gBAAA,cAAc,EAAE,WAAW;iBAC3B,mBAAmB;iBACnB,gBAAgB;CAChB,gBAAA,WAAW,EAAE,MAAM,iBAAiB,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;CAC9E,aAAA,CAAC;;CAGJ,QAAA,IACE,IAAI,KAAK,IAAI,CAAC;gBACX,IAAI,CAAC,WAAW,KAAK;CACrB,eAAA,IAAI,CAAC,gBAAgB,KAAK,gBAAgB,EAC7C;CACA,YAAA,OAAO,IAAI;;CAGb,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI;CAChB,QAAA,IAAI,CAAC,WAAW,GAAG,WAAW;CAC9B,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;SAExC,iBAAiB,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;CAE1D,QAAA,OAAO,IAAI;;CAGb;;;CAGG;KACH,UAAU,GAAA;CACR,QAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;CACxB,YAAA,QAAQ,EAAE,IAAI;CACf,SAAA,CAAC;SACF,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,0BAA0B,CAAC;;CAGjE;;;CAGG;KACH,YAAY,GAAA;CACV,QAAA,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;CACxB,YAAA,QAAQ,EAAE,KAAK;CAChB,SAAA,CAAC;SACF,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,0BAA0B,CAAC;;CAGpE;;CAEG;KACH,OAAO,GAAA;CACL,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;SACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,qBAAqB,CAAC;CAC9D,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI;;CAG/B;;;CAGG;KACH,uBAAuB,GAAA;CACrB,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;aACtB,IAAI,QAAQ,GAA2B,EAAE;aAEzC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,UAAU,EAAE;iBAC5C,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;iBACnE,MAAM,cAAc,GAAGC,0BAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC;CAE5E,gBAAA,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC;;kBAC7D;CACL,gBAAA,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;;CAG/B,YAAA,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC;;;CAG7C;CAED;;CAEG;CACa,SAAA,qBAAqB,CACnC,SAA+C,EAC/C,OAA+C,EAAA;KAE/C,OAAO,KAAK,IAAG;;;;CAIb,QAAA,IAAI,CAAE,KAAK,CAAC,MAAqC,CAAC,gBAAgB,EAAE;CAClE,YAAA,OAAO,EAAoC;;SAG7C,OAAO,IAAI,aAAa,CAAI,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC;CACxD,KAAC;CACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [0, 1, 2, 4, 5, 6, 7]}