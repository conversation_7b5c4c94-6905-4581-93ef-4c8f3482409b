{"name": "@tiptap/react", "description": "React components for tiptap", "version": "2.14.0", "homepage": "https://tiptap.dev", "keywords": ["tiptap", "tiptap react components"], "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/ueberdosis"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.js", "umd": "dist/index.umd.js", "types": "dist/index.d.ts", "type": "module", "files": ["src", "dist"], "dependencies": {"@tiptap/extension-bubble-menu": "^2.14.0", "@tiptap/extension-floating-menu": "^2.14.0", "@types/use-sync-external-store": "^0.0.6", "fast-deep-equal": "^3", "use-sync-external-store": "^1"}, "devDependencies": {"@tiptap/core": "^2.14.0", "@tiptap/pm": "^2.14.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "peerDependencies": {"@tiptap/core": "^2.7.0", "@tiptap/pm": "^2.7.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "repository": {"type": "git", "url": "https://github.com/ueberdosis/tiptap", "directory": "packages/react"}, "sideEffects": false, "scripts": {"clean": "rm -rf dist", "build": "npm run clean && rollup -c"}}