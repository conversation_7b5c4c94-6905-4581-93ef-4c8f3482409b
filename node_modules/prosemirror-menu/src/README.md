This module defines a number of building blocks for ProseMirror menus,
along with a [menu bar](#menu.menuBar) implementation.

When using this module, you should make sure its
[`style/menu.css`](https://github.com/ProseMirror/prosemirror-menu/blob/master/style/menu.css)
file is loaded into your page.

@MenuElement
@MenuItem
@MenuItemSpec
@IconSpec
@Dropdown
@DropdownSubmenu
@menuBar

This module exports the following pre-built items or item
constructors:

@joinUpItem
@liftItem
@selectParentNodeItem
@undoItem
@redoItem
@wrapItem
@blockTypeItem

To construct your own items, these icons may be useful:

@icons

@renderGrouped
