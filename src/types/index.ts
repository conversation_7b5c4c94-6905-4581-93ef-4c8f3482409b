export interface News {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  category_id: string;
  author_id: string;
  status: 'draft' | 'published' | 'archived';
  featured_image?: string;
  published_at?: string;
  created_at: string;
  updated_at: string;
  view_count: number;
  tags?: string[];
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'editor';
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface NewsWithDetails extends News {
  category: Category;
  author: User;
}

export interface CreateNewsData {
  title: string;
  content: string;
  excerpt: string;
  category_id: string;
  status: 'draft' | 'published';
  featured_image?: string;
  tags?: string[];
}

export interface UpdateNewsData extends Partial<CreateNewsData> {
  id: string;
}

export interface NewsFilters {
  category_id?: string;
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
}
