import type { News, Category, User, CreateNewsData, UpdateNewsData, NewsFilters, NewsWithDetails } from '@/types';

// 模拟数据
const mockCategories: Category[] = [
  {
    id: '1',
    name: '公司新闻',
    description: '公司内部新闻和公告',
    slug: 'company-news',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: '行业动态',
    description: '行业相关新闻和趋势',
    slug: 'industry-news',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '3',
    name: '产品发布',
    description: '新产品发布和更新',
    slug: 'product-releases',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

const mockUsers: User[] = [
  {
    id: 'user1',
    email: '<EMAIL>',
    name: '系统管理员',
    role: 'admin',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'user2',
    email: '<EMAIL>',
    name: '编辑',
    role: 'editor',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

let mockNews: NewsWithDetails[] = [
  {
    id: 'news1',
    title: '公司2024年度总结大会成功举办',
    content: '<h2>会议概况</h2><p>2024年12月15日，我公司在总部大楼成功举办了年度总结大会。本次大会回顾了过去一年的重要成就，并对未来发展进行了规划。</p><h3>主要议题</h3><ul><li>2024年业绩回顾</li><li>优秀员工表彰</li><li>2025年发展规划</li></ul><p>会议取得了圆满成功，为公司未来发展奠定了坚实基础。</p>',
    excerpt: '公司年度总结大会成功举办，回顾成就，规划未来发展方向。',
    category_id: '1',
    author_id: 'user1',
    status: 'published',
    published_at: '2024-12-15T10:00:00Z',
    created_at: '2024-12-15T09:00:00Z',
    updated_at: '2024-12-15T09:00:00Z',
    view_count: 156,
    tags: ['年度总结', '会议', '规划'],
    category: mockCategories[0],
    author: mockUsers[0],
  },
  {
    id: 'news2',
    title: '新产品发布：智能办公系统V2.0',
    content: '<h2>产品介绍</h2><p>我们很高兴地宣布，智能办公系统V2.0正式发布！新版本带来了许多令人兴奋的功能和改进。</p><h3>主要新功能</h3><ul><li>AI智能助手</li><li>实时协作编辑</li><li>移动端优化</li><li>数据可视化仪表板</li></ul><p>欢迎所有用户升级体验！</p>',
    excerpt: '智能办公系统V2.0正式发布，带来AI助手、实时协作等新功能。',
    category_id: '3',
    author_id: 'user2',
    status: 'published',
    published_at: '2024-12-10T14:00:00Z',
    created_at: '2024-12-10T13:00:00Z',
    updated_at: '2024-12-10T13:00:00Z',
    view_count: 89,
    tags: ['产品发布', '智能办公', 'AI'],
    category: mockCategories[2],
    author: mockUsers[1],
  },
  {
    id: 'news3',
    title: '行业报告：2024年技术发展趋势分析',
    content: '<h2>报告摘要</h2><p>根据最新的行业调研数据，2024年技术发展呈现出以下几个重要趋势...</p><h3>主要趋势</h3><ol><li>人工智能技术的普及应用</li><li>云计算服务的深度整合</li><li>数据安全重要性日益凸显</li></ol><p>这些趋势将深刻影响未来几年的技术发展方向。</p>',
    excerpt: '深度分析2024年技术发展趋势，包括AI、云计算、数据安全等重点领域。',
    category_id: '2',
    author_id: 'user1',
    status: 'draft',
    created_at: '2024-12-08T16:00:00Z',
    updated_at: '2024-12-08T16:00:00Z',
    view_count: 23,
    tags: ['行业报告', '技术趋势', '分析'],
    category: mockCategories[1],
    author: mockUsers[0],
  },
];

// 模拟 API 延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟新闻 API
export const mockNewsApi = {
  async getNews(filters: NewsFilters = {}) {
    await delay(500); // 模拟网络延迟
    
    let filteredNews = [...mockNews];
    
    if (filters.category_id) {
      filteredNews = filteredNews.filter(news => news.category_id === filters.category_id);
    }
    
    if (filters.status) {
      filteredNews = filteredNews.filter(news => news.status === filters.status);
    }
    
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredNews = filteredNews.filter(news => 
        news.title.toLowerCase().includes(searchLower) ||
        news.content.toLowerCase().includes(searchLower)
      );
    }
    
    // 简单分页
    const limit = filters.limit || 10;
    const offset = ((filters.page || 1) - 1) * limit;
    
    return filteredNews.slice(offset, offset + limit);
  },

  async getNewsById(id: string) {
    await delay(300);
    const news = mockNews.find(n => n.id === id);
    if (!news) throw new Error('新闻不存在');
    return news;
  },

  async createNews(newsData: CreateNewsData) {
    await delay(800);
    const newNews: NewsWithDetails = {
      id: `news${Date.now()}`,
      ...newsData,
      author_id: 'user1',
      view_count: 0,
      published_at: newsData.status === 'published' ? new Date().toISOString() : undefined,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      category: mockCategories.find(c => c.id === newsData.category_id) || mockCategories[0],
      author: mockUsers[0],
    };
    mockNews.unshift(newNews);
    return newNews;
  },

  async updateNews(newsData: UpdateNewsData) {
    await delay(800);
    const index = mockNews.findIndex(n => n.id === newsData.id);
    if (index === -1) throw new Error('新闻不存在');
    
    const { id, ...updateData } = newsData;
    mockNews[index] = {
      ...mockNews[index],
      ...updateData,
      updated_at: new Date().toISOString(),
      published_at: updateData.status === 'published' ? new Date().toISOString() : mockNews[index].published_at,
      category: updateData.category_id ? 
        mockCategories.find(c => c.id === updateData.category_id) || mockNews[index].category :
        mockNews[index].category,
    };
    return mockNews[index];
  },

  async deleteNews(id: string) {
    await delay(500);
    const index = mockNews.findIndex(n => n.id === id);
    if (index === -1) throw new Error('新闻不存在');
    mockNews.splice(index, 1);
  },

  async incrementViewCount(id: string) {
    await delay(200);
    const news = mockNews.find(n => n.id === id);
    if (news) {
      news.view_count += 1;
    }
  },
};

// 模拟分类 API
export const mockCategoryApi = {
  async getCategories() {
    await delay(300);
    return [...mockCategories];
  },

  async createCategory(categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>) {
    await delay(500);
    const newCategory: Category = {
      id: `cat${Date.now()}`,
      ...categoryData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    mockCategories.push(newCategory);
    return newCategory;
  },

  async updateCategory(id: string, categoryData: Partial<Category>) {
    await delay(500);
    const index = mockCategories.findIndex(c => c.id === id);
    if (index === -1) throw new Error('分类不存在');
    
    mockCategories[index] = {
      ...mockCategories[index],
      ...categoryData,
      updated_at: new Date().toISOString(),
    };
    return mockCategories[index];
  },

  async deleteCategory(id: string) {
    await delay(500);
    const index = mockCategories.findIndex(c => c.id === id);
    if (index === -1) throw new Error('分类不存在');
    mockCategories.splice(index, 1);
  },
};

// 模拟用户 API
export const mockUserApi = {
  async getCurrentUser() {
    await delay(200);
    return mockUsers[0]; // 返回管理员用户
  },

  async signIn(email: string, password: string) {
    await delay(1000);
    const user = mockUsers.find(u => u.email === email);
    if (!user) throw new Error('用户不存在');
    return { user };
  },

  async signOut() {
    await delay(300);
  },
};
