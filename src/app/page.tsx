'use client';

import { useState } from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import LoginForm from '@/components/LoginForm';
import LoginModal from '@/components/LoginModal';
import PublicNewsDisplay from '@/components/PublicNewsDisplay';
import Navigation from '@/components/Navigation';
import NewsList from '@/components/NewsList';
import NewsForm from '@/components/NewsForm';
import NewsViewer from '@/components/NewsViewer';
import NewsImport from '@/components/NewsImport';
import NewsImportDebug from '@/components/NewsImportDebug';
import QuickImportTest from '@/components/QuickImportTest';
import RefreshTest from '@/components/RefreshTest';
import SyncTest from '@/components/SyncTest';
import ForceRefreshHomepage from '@/components/ForceRefreshHomepage';
import PublishedNewsStats from '@/components/PublishedNewsStats';
import UserManagement from '@/components/UserManagement';
import NewsApproval from '@/components/NewsApproval';
import { newsApi } from '@/lib/api';
import type { NewsWithDetails, CreateNewsData } from '@/types';

export default function Home() {
  const [currentView, setCurrentView] = useState('news');
  const [showForm, setShowForm] = useState(false);
  const [showViewer, setShowViewer] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [editingNews, setEditingNews] = useState<NewsWithDetails | null>(null);
  const [viewingNews, setViewingNews] = useState<NewsWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const { user, loading } = useAuth();

  const handleCreateNews = () => {
    setEditingNews(null);
    setShowForm(true);
  };

  const handleEditNews = (news: NewsWithDetails) => {
    setEditingNews(news);
    setShowForm(true);
  };

  const handleViewNews = (news: NewsWithDetails) => {
    setViewingNews(news);
    setShowViewer(true);
  };

  const handleDeleteNews = async (id: string) => {
    if (!confirm('确定要删除这条新闻吗？')) {
      return;
    }

    try {
      await newsApi.deleteNews(id);
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error('删除新闻失败:', error);
      alert('删除失败，请重试');
    }
  };

  const handleSubmitNews = async (data: CreateNewsData) => {
    try {
      setIsLoading(true);

      if (editingNews) {
        await newsApi.updateNews({ ...data, id: editingNews.id });
      } else {
        await newsApi.createNews(data);
      }

      setShowForm(false);
      setEditingNews(null);
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error('保存新闻失败:', error);
      alert('保存失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingNews(null);
  };

  const handleCloseViewer = () => {
    setShowViewer(false);
    setViewingNews(null);
    setRefreshTrigger(prev => prev + 1); // 刷新列表以更新浏览量
  };

  const handleViewChange = (view: string) => {
    setCurrentView(view);
    setShowForm(false);
    setShowViewer(false);
    setEditingNews(null);
    setViewingNews(null);
  };

  const handleShowLogin = () => {
    setShowLoginModal(true);
  };

  const handleCloseLogin = () => {
    setShowLoginModal(false);
  };

  // 显示加载状态
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // 如果用户未登录，显示公开新闻页面
  if (!user) {
    return (
      <>
        <PublicNewsDisplay onLogin={handleShowLogin} />
        <LoginModal
          isOpen={showLoginModal}
          onClose={handleCloseLogin}
        />
      </>
    );
  }

  // 如果正在显示表单，渲染表单页面
  if (showForm) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation currentView={currentView} onViewChange={handleViewChange} />
        <div className="py-8">
          <NewsForm
            initialData={editingNews || undefined}
            onSubmit={handleSubmitNews}
            onCancel={handleCancel}
            isLoading={isLoading}
          />
        </div>
      </div>
    );
  }

  // 如果正在查看新闻，渲染新闻查看器
  if (showViewer && viewingNews) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation currentView={currentView} onViewChange={handleViewChange} />
        <NewsViewer
          news={viewingNews}
          onClose={handleCloseViewer}
        />
      </div>
    );
  }

  // 渲染主要内容
  const renderContent = () => {
    switch (currentView) {
      case 'news':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">新闻管理</h2>
              <button
                onClick={handleCreateNews}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                创建新闻
              </button>
            </div>
            <NewsList
              onEdit={handleEditNews}
              onDelete={handleDeleteNews}
              onView={handleViewNews}
              refreshTrigger={refreshTrigger}
            />
          </div>
        );
      case 'import':
        return (
          <NewsImport
            onImportComplete={() => {
              setCurrentView('news');
              setRefreshTrigger(prev => prev + 1);
            }}
          />
        );
      case 'debug':
        return <NewsImportDebug />;
      case 'test':
        return <QuickImportTest />;
      case 'refresh-test':
        return <RefreshTest />;
      case 'sync-test':
        return <SyncTest />;
      case 'force-refresh':
        return <ForceRefreshHomepage />;
      case 'published-stats':
        return <PublishedNewsStats />;
      case 'approval':
        return <NewsApproval />;
      case 'users':
        return <UserManagement />;
      default:
        return <div>页面不存在</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation currentView={currentView} onViewChange={handleViewChange} />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderContent()}
      </main>
    </div>
  );
}
