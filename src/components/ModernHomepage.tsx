'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  MagnifyingGlassIcon,
  ArrowRightOnRectangleIcon,
  ArrowPathIcon,
  EyeIcon,
  CalendarIcon,
  UserIcon,
  FolderIcon,
  TagIcon,
  FireIcon,
  ClockIcon,
  TrendingUpIcon,
  NewspaperIcon
} from '@heroicons/react/24/outline';
import { newsApi, categoryApi } from '@/lib/api';
import { newsEvents } from '@/lib/eventBus';
import NewsViewer from './NewsViewer';
import type { NewsWithDetails, Category, NewsFilters } from '@/types';

interface ModernHomepageProps {
  onLogin: () => void;
}

export default function ModernHomepage({ onLogin }: ModernHomepageProps) {
  const [news, setNews] = useState<NewsWithDetails[]>([]);
  const [featuredNews, setFeaturedNews] = useState<NewsWithDetails[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedNews, setSelectedNews] = useState<NewsWithDetails | null>(null);
  const [showViewer, setShowViewer] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeCategory, setActiveCategory] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    loadData();
    loadCategories();
  }, []);

  // 监听新闻数据变更事件
  useEffect(() => {
    const handleRefreshNeeded = () => {
      console.log('收到新闻数据变更事件，刷新首页数据...');
      loadData();
    };

    newsEvents.onRefreshNeeded(handleRefreshNeeded);
    return () => {
      newsEvents.offRefreshNeeded(handleRefreshNeeded);
    };
  }, []);

  // 定期刷新数据
  useEffect(() => {
    const interval = setInterval(() => {
      loadData();
    }, 60000); // 每分钟刷新一次

    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // 加载所有已发布的新闻
      const allNews = await newsApi.getNews({
        status: 'published',
        limit: 100
      });

      console.log('首页加载已发布新闻:', allNews.length, '条');

      // 按优先级排序：管理员admin账户的新闻优先显示
      const sortedNews = [...allNews].sort((a, b) => {
        // 管理员(user1)的新闻优先
        const aIsAdmin = a.author_id === 'user1';
        const bIsAdmin = b.author_id === 'user1';

        if (aIsAdmin && !bIsAdmin) return -1;
        if (!aIsAdmin && bIsAdmin) return 1;

        // 同级别内按发布时间排序（最新的在前）
        const aTime = new Date(a.published_at || a.created_at).getTime();
        const bTime = new Date(b.published_at || b.created_at).getTime();
        return bTime - aTime;
      });

      // 设置精选新闻（优先选择管理员的新闻，然后按浏览量）
      const adminNews = sortedNews.filter(item => item.author_id === 'user1');
      const otherNews = sortedNews.filter(item => item.author_id !== 'user1')
        .sort((a, b) => b.view_count - a.view_count);

      const featured = [
        ...adminNews.slice(0, 2), // 优先显示2条管理员新闻
        ...otherNews.slice(0, 1)  // 再显示1条其他高浏览量新闻
      ].slice(0, 3);

      setFeaturedNews(featured);
      setNews(sortedNews);

    } catch (error) {
      console.error('加载新闻失败:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadCategories = async () => {
    try {
      const data = await categoryApi.getCategories();
      setCategories(data);
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const handleManualRefresh = async () => {
    setRefreshing(true);
    await loadData();
  };

  const handleViewNews = (newsItem: NewsWithDetails) => {
    setSelectedNews(newsItem);
    setShowViewer(true);
  };

  const handleCloseViewer = () => {
    setShowViewer(false);
    setSelectedNews(null);
    loadData(); // 刷新数据以更新浏览量
  };

  // 筛选新闻
  const filteredNews = news.filter(item => {
    // 特殊处理管理员筛选
    if (activeCategory === 'admin') {
      const matchesAdmin = item.author_id === 'user1';
      const matchesSearch = !searchQuery ||
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.excerpt.toLowerCase().includes(searchQuery.toLowerCase());
      return matchesAdmin && matchesSearch;
    }

    // 普通分类筛选
    const matchesCategory = !activeCategory || item.category_id === activeCategory;
    const matchesSearch = !searchQuery ||
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.excerpt.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // 获取最新新闻（最近7天）
  const recentNews = news.filter(item => {
    const publishedDate = new Date(item.published_at || item.created_at);
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    return publishedDate >= sevenDaysAgo;
  });

  // 获取热门新闻（浏览量前10）
  const popularNews = [...news]
    .sort((a, b) => b.view_count - a.view_count)
    .slice(0, 10);

  // 获取管理员发布的新闻数量
  const adminNews = news.filter(item => item.author_id === 'user1');

  if (showViewer && selectedNews) {
    return (
      <NewsViewer
        news={selectedNews}
        onClose={handleCloseViewer}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* 现代化头部 */}
      <header className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <NewspaperIcon className="h-5 w-5 text-white" />
                </div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  荣联科技新闻中心
                </h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={handleManualRefresh}
                disabled={refreshing}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white/80 hover:bg-white hover:shadow-md transition-all duration-200 disabled:opacity-50"
                title={refreshing ? "刷新中..." : "刷新新闻"}
              >
                <ArrowPathIcon className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              </button>
              
              <button
                onClick={onLogin}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-md hover:shadow-lg transition-all duration-200"
              >
                <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                管理员登录
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 统计概览 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-sm border border-white/20">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <NewspaperIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总新闻数</p>
                <p className="text-2xl font-bold text-gray-900">{news.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-sm border border-white/20">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <ClockIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">最近7天</p>
                <p className="text-2xl font-bold text-gray-900">{recentNews.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-sm border border-white/20">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <EyeIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">总浏览量</p>
                <p className="text-2xl font-bold text-gray-900">
                  {news.reduce((sum, item) => sum + item.view_count, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-sm border border-white/20">
            <div className="flex items-center">
              <div className="p-2 bg-orange-100 rounded-lg">
                <UserIcon className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">官方发布</p>
                <p className="text-2xl font-bold text-gray-900">{adminNews.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-white/20 p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索新闻标题或内容..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/80"
                />
              </div>
            </div>
            
            <div className="flex gap-2 overflow-x-auto pb-2">
              <button
                onClick={() => setActiveCategory('')}
                className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                  activeCategory === ''
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-white/80 text-gray-700 hover:bg-white hover:shadow-sm'
                }`}
              >
                全部
              </button>
              <button
                onClick={() => setActiveCategory('admin')}
                className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                  activeCategory === 'admin'
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-md'
                    : 'bg-white/80 text-gray-700 hover:bg-white hover:shadow-sm'
                }`}
              >
                官方发布
              </button>
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-all duration-200 ${
                    activeCategory === category.id 
                      ? 'bg-blue-600 text-white shadow-md' 
                      : 'bg-white/80 text-gray-700 hover:bg-white hover:shadow-sm'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {/* 精选新闻 */}
            {featuredNews.length > 0 && (
              <section className="mb-12">
                <div className="flex items-center mb-6">
                  <FireIcon className="h-6 w-6 text-red-500 mr-2" />
                  <h2 className="text-2xl font-bold text-gray-900">精选新闻</h2>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {featuredNews.map((item, index) => (
                    <article
                      key={item.id}
                      className={`group cursor-pointer ${
                        index === 0 ? 'lg:col-span-2 lg:row-span-2' : ''
                      }`}
                      onClick={() => handleViewNews(item)}
                    >
                      <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-white/20 overflow-hidden hover:shadow-lg transition-all duration-300 h-full">
                        <div className={`p-6 ${index === 0 ? 'lg:p-8' : ''}`}>
                          <div className="flex items-center mb-3">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              精选
                            </span>
                            {item.author_id === 'user1' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-100 to-blue-100 text-purple-800 ml-2">
                                官方发布
                              </span>
                            )}
                            <span className="ml-2 text-xs text-gray-500">
                              {item.category.name}
                            </span>
                          </div>

                          <h3 className={`font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors ${
                            index === 0 ? 'text-2xl lg:text-3xl' : 'text-lg'
                          }`}>
                            {item.title}
                          </h3>

                          <p className={`text-gray-600 mb-4 ${
                            index === 0 ? 'text-base lg:text-lg' : 'text-sm'
                          }`}>
                            {item.excerpt}
                          </p>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center text-sm text-gray-500">
                              <UserIcon className="h-4 w-4 mr-1" />
                              <span>{item.author.name}</span>
                              <CalendarIcon className="h-4 w-4 ml-3 mr-1" />
                              <span>
                                {format(new Date(item.published_at || item.created_at), 'MM月dd日', { locale: zhCN })}
                              </span>
                            </div>
                            <div className="flex items-center text-sm text-gray-500">
                              <EyeIcon className="h-4 w-4 mr-1" />
                              <span>{item.view_count}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </article>
                  ))}
                </div>
              </section>
            )}

            {/* 最新新闻 */}
            <section className="mb-12">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <ClockIcon className="h-6 w-6 text-green-500 mr-2" />
                  <h2 className="text-2xl font-bold text-gray-900">
                    {activeCategory || searchQuery ? '筛选结果' : '最新新闻'}
                  </h2>
                  {(activeCategory || searchQuery) && (
                    <span className="ml-3 text-sm text-gray-500">
                      共 {filteredNews.length} 条新闻
                    </span>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg ${
                      viewMode === 'grid'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white/80 text-gray-600 hover:bg-white'
                    }`}
                  >
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg ${
                      viewMode === 'list'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white/80 text-gray-600 hover:bg-white'
                    }`}
                  >
                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>

              {filteredNews.length === 0 ? (
                <div className="text-center py-12 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-white/20">
                  <div className="text-gray-500">
                    <NewspaperIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-medium mb-2">暂无新闻</h3>
                    <p>
                      {searchQuery || activeCategory
                        ? '没有找到符合条件的新闻'
                        : '目前没有已发布的新闻内容'
                      }
                    </p>
                    {!searchQuery && !activeCategory && (
                      <p className="text-sm mt-2">管理员可以登录后台导入或创建新闻</p>
                    )}
                  </div>
                </div>
              ) : (
                <div className={
                  viewMode === 'grid'
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                    : 'space-y-4'
                }>
                  {filteredNews.map((item) => (
                    <article
                      key={item.id}
                      className={`group cursor-pointer ${
                        viewMode === 'grid'
                          ? 'bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-white/20 overflow-hidden hover:shadow-lg transition-all duration-300'
                          : 'bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-white/20 p-6 hover:shadow-lg transition-all duration-300 flex items-center space-x-6'
                      }`}
                      onClick={() => handleViewNews(item)}
                    >
                      {viewMode === 'grid' ? (
                        <div className="p-6">
                          <div className="flex items-center mb-3">
                            {item.author_id === 'user1' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-100 to-blue-100 text-purple-800 mr-2">
                                官方发布
                              </span>
                            )}
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {item.category.name}
                            </span>
                          </div>

                          <h3 className="text-lg font-semibold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
                            {item.title}
                          </h3>

                          <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                            {item.excerpt}
                          </p>

                          {item.tags && item.tags.length > 0 && (
                            <div className="mb-4">
                              <div className="flex flex-wrap gap-1">
                                {item.tags.slice(0, 3).map((tag, index) => (
                                  <span
                                    key={index}
                                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700"
                                  >
                                    {tag}
                                  </span>
                                ))}
                                {item.tags.length > 3 && (
                                  <span className="text-xs text-gray-500">
                                    +{item.tags.length - 3}
                                  </span>
                                )}
                              </div>
                            </div>
                          )}

                          <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                            <div className="flex items-center text-xs text-gray-500">
                              <UserIcon className="h-4 w-4 mr-1" />
                              <span>{item.author.name}</span>
                              <CalendarIcon className="h-4 w-4 ml-3 mr-1" />
                              <span>
                                {format(new Date(item.published_at || item.created_at), 'MM月dd日', { locale: zhCN })}
                              </span>
                            </div>
                            <div className="flex items-center text-xs text-gray-500">
                              <EyeIcon className="h-4 w-4 mr-1" />
                              <span>{item.view_count}</span>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div className="flex-1">
                            <div className="flex items-center mb-2">
                              {item.author_id === 'user1' && (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-purple-100 to-blue-100 text-purple-800 mr-2">
                                  官方发布
                                </span>
                              )}
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {item.category.name}
                              </span>
                            </div>

                            <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                              {item.title}
                            </h3>

                            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                              {item.excerpt}
                            </p>

                            <div className="flex items-center text-xs text-gray-500">
                              <UserIcon className="h-4 w-4 mr-1" />
                              <span>{item.author.name}</span>
                              <CalendarIcon className="h-4 w-4 ml-3 mr-1" />
                              <span>
                                {format(new Date(item.published_at || item.created_at), 'MM月dd日', { locale: zhCN })}
                              </span>
                              <EyeIcon className="h-4 w-4 ml-3 mr-1" />
                              <span>{item.view_count}</span>
                            </div>
                          </div>

                          {item.tags && item.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {item.tags.slice(0, 2).map((tag, index) => (
                                <span
                                  key={index}
                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          )}
                        </>
                      )}
                    </article>
                  ))}
                </div>
              )}
            </section>
          </>
        )}
      </main>

      {/* 现代化页脚 */}
      <footer className="bg-white/80 backdrop-blur-md border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <NewspaperIcon className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  荣联科技新闻中心
                </h3>
              </div>
              <p className="text-gray-600 text-sm mb-4">
                荣联科技官方新闻发布平台，为您提供最新的公司动态、技术资讯和行业洞察。
              </p>
              <div className="flex flex-wrap gap-4">
                <div className="text-sm text-gray-500">
                  <span className="font-medium">总新闻:</span> {news.length} 条
                </div>
                <div className="text-sm text-gray-500">
                  <span className="font-medium">官方发布:</span> {adminNews.length} 条
                </div>
                <div className="text-sm text-gray-500">
                  <span className="font-medium">总浏览:</span> {news.reduce((sum, item) => sum + item.view_count, 0).toLocaleString()} 次
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-4">新闻分类</h4>
              <ul className="space-y-2">
                {categories.slice(0, 4).map((category) => (
                  <li key={category.id}>
                    <button
                      onClick={() => setActiveCategory(category.id)}
                      className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
                    >
                      {category.name}
                    </button>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-4">快速链接</h4>
              <ul className="space-y-2">
                <li>
                  <button
                    onClick={() => {
                      setActiveCategory('');
                      setSearchQuery('');
                    }}
                    className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
                  >
                    全部新闻
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => {
                      setActiveCategory('admin');
                      setSearchQuery('');
                    }}
                    className="text-sm text-gray-600 hover:text-purple-600 transition-colors"
                  >
                    官方发布
                  </button>
                </li>
                <li>
                  <button
                    onClick={onLogin}
                    className="text-sm text-gray-600 hover:text-blue-600 transition-colors"
                  >
                    管理员登录
                  </button>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-200 mt-8 pt-8 text-center">
            <p className="text-sm text-gray-500">
              &copy; 2024 荣联科技新闻中心. 保留所有权利.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
