{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key';\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// 数据库表结构\nexport const TABLES = {\n  NEWS: 'news',\n  CATEGORIES: 'categories',\n  USERS: 'users',\n} as const;\n"], "names": [], "mappings": ";;;;AAEoB;AAFpB;;AAEA,MAAM,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,IAAI;AAC5D,MAAM,kBAAkB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI;AAE9D,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,SAAS;IACpB,MAAM;IACN,YAAY;IACZ,OAAO;AACT", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/lib/mockData.ts"], "sourcesContent": ["import type { News, Category, User, CreateNewsData, UpdateNewsData, NewsFilters, NewsWithDetails } from '@/types';\n\n// 模拟数据\nconst mockCategories: Category[] = [\n  {\n    id: '1',\n    name: '公司新闻',\n    description: '公司内部新闻和公告',\n    slug: 'company-news',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: '2',\n    name: '行业动态',\n    description: '行业相关新闻和趋势',\n    slug: 'industry-news',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n  {\n    id: '3',\n    name: '产品发布',\n    description: '新产品发布和更新',\n    slug: 'product-releases',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  },\n];\n\nlet mockUsers: User[] = [\n  {\n    id: 'user1',\n    email: '<EMAIL>',\n    name: '系统管理员',\n    role: 'admin',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n  },\n  {\n    id: 'user2',\n    email: '<EMAIL>',\n    name: '编辑',\n    role: 'editor',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n    is_active: true,\n  },\n  {\n    id: 'user3',\n    email: '<EMAIL>',\n    name: '编辑小王',\n    role: 'editor',\n    created_at: '2024-01-02T00:00:00Z',\n    updated_at: '2024-01-02T00:00:00Z',\n    is_active: true,\n  },\n];\n\n// 模拟密码存储（实际应用中应该使用加密）\nconst mockPasswords: Record<string, string> = {\n  '<EMAIL>': 'admin123',\n  '<EMAIL>': 'editor123',\n  '<EMAIL>': 'editor123',\n};\n\nlet mockNews: NewsWithDetails[] = [\n  {\n    id: 'news1',\n    title: '公司2024年度总结大会成功举办',\n    content: '<h2>会议概况</h2><p>2024年12月15日，我公司在总部大楼成功举办了年度总结大会。本次大会回顾了过去一年的重要成就，并对未来发展进行了规划。</p><h3>主要议题</h3><ul><li>2024年业绩回顾</li><li>优秀员工表彰</li><li>2025年发展规划</li></ul><p>会议取得了圆满成功，为公司未来发展奠定了坚实基础。</p>',\n    excerpt: '公司年度总结大会成功举办，回顾成就，规划未来发展方向。',\n    category_id: '1',\n    author_id: 'user1',\n    status: 'published',\n    published_at: '2024-12-15T10:00:00Z',\n    created_at: '2024-12-15T09:00:00Z',\n    updated_at: '2024-12-15T09:00:00Z',\n    view_count: 156,\n    tags: ['年度总结', '会议', '规划'],\n    category: mockCategories[0],\n    author: mockUsers[0],\n  },\n  {\n    id: 'news2',\n    title: '新产品发布：智能办公系统V2.0',\n    content: '<h2>产品介绍</h2><p>我们很高兴地宣布，智能办公系统V2.0正式发布！新版本带来了许多令人兴奋的功能和改进。</p><h3>主要新功能</h3><ul><li>AI智能助手</li><li>实时协作编辑</li><li>移动端优化</li><li>数据可视化仪表板</li></ul><p>欢迎所有用户升级体验！</p>',\n    excerpt: '智能办公系统V2.0正式发布，带来AI助手、实时协作等新功能。',\n    category_id: '3',\n    author_id: 'user2',\n    status: 'published',\n    published_at: '2024-12-10T14:00:00Z',\n    created_at: '2024-12-10T13:00:00Z',\n    updated_at: '2024-12-10T13:00:00Z',\n    view_count: 89,\n    tags: ['产品发布', '智能办公', 'AI'],\n    category: mockCategories[2],\n    author: mockUsers[1],\n  },\n  {\n    id: 'news3',\n    title: '行业报告：2024年技术发展趋势分析',\n    content: '<h2>报告摘要</h2><p>根据最新的行业调研数据，2024年技术发展呈现出以下几个重要趋势...</p><h3>主要趋势</h3><ol><li>人工智能技术的普及应用</li><li>云计算服务的深度整合</li><li>数据安全重要性日益凸显</li></ol><p>这些趋势将深刻影响未来几年的技术发展方向。</p>',\n    excerpt: '深度分析2024年技术发展趋势，包括AI、云计算、数据安全等重点领域。',\n    category_id: '2',\n    author_id: 'user1',\n    status: 'draft',\n    created_at: '2024-12-08T16:00:00Z',\n    updated_at: '2024-12-08T16:00:00Z',\n    view_count: 23,\n    tags: ['行业报告', '技术趋势', '分析'],\n    category: mockCategories[1],\n    author: mockUsers[0],\n  },\n  {\n    id: 'news4',\n    title: '新员工培训计划启动通知',\n    content: '<h2>培训计划概述</h2><p>为了帮助新员工更好地融入公司文化，我们将启动全新的员工培训计划。</p><h3>培训内容</h3><ul><li>公司文化和价值观</li><li>业务流程介绍</li><li>技能培训</li><li>团队建设活动</li></ul><p>请各部门积极配合，确保培训效果。</p>',\n    excerpt: '公司启动新员工培训计划，包含文化介绍、业务流程、技能培训等内容。',\n    category_id: '1',\n    author_id: 'user2',\n    status: 'pending',\n    created_at: '2024-12-09T10:00:00Z',\n    updated_at: '2024-12-09T10:00:00Z',\n    view_count: 0,\n    tags: ['培训', '新员工', '通知'],\n    category: mockCategories[0],\n    author: mockUsers[1],\n  },\n  {\n    id: 'news5',\n    title: '技术分享：React 19 新特性解析',\n    content: '<h2>React 19 重要更新</h2><p>React 19 带来了许多令人兴奋的新特性和改进...</p><h3>主要特性</h3><ul><li>并发渲染优化</li><li>新的 Hooks API</li><li>服务器组件增强</li><li>性能提升</li></ul><p>让我们一起探索这些新特性如何改善开发体验。</p>',\n    excerpt: 'React 19 新特性详细解析，包括并发渲染、新 Hooks、服务器组件等。',\n    category_id: '3',\n    author_id: 'user3',\n    status: 'published',\n    published_at: '2024-12-09T15:00:00Z',\n    created_at: '2024-12-09T14:00:00Z',\n    updated_at: '2024-12-09T15:00:00Z',\n    view_count: 45,\n    tags: ['React', '技术分享', '前端'],\n    category: mockCategories[2],\n    author: mockUsers[2],\n  },\n  {\n    id: 'news6',\n    title: '荣联科技荣获\"年度最佳创新企业\"奖项',\n    content: '<h2>获奖详情</h2><p>在2024年度科技创新大会上，荣联科技凭借在人工智能和云计算领域的突出贡献，荣获\"年度最佳创新企业\"奖项。</p><h3>获奖理由</h3><ul><li>技术创新能力突出</li><li>产品市场表现优异</li><li>社会责任履行到位</li></ul><p>这一荣誉是对我们团队努力的最好认可。</p>',\n    excerpt: '荣联科技在2024年度科技创新大会上荣获\"年度最佳创新企业\"奖项。',\n    category_id: '1',\n    author_id: 'user1',\n    status: 'published',\n    published_at: '2024-12-07T09:00:00Z',\n    created_at: '2024-12-07T08:00:00Z',\n    updated_at: '2024-12-07T09:00:00Z',\n    view_count: 234,\n    tags: ['获奖', '创新', '企业荣誉'],\n    category: mockCategories[0],\n    author: mockUsers[0],\n  },\n  {\n    id: 'news7',\n    title: '云计算服务升级：性能提升50%',\n    content: '<h2>升级内容</h2><p>经过三个月的技术攻关，我们的云计算服务平台完成了重大升级。</p><h3>主要改进</h3><ul><li>计算性能提升50%</li><li>存储容量扩大一倍</li><li>网络延迟降低30%</li><li>新增AI加速功能</li></ul><p>升级后的服务将为客户提供更好的使用体验。</p>',\n    excerpt: '云计算服务平台完成重大升级，性能提升50%，存储容量扩大一倍。',\n    category_id: '3',\n    author_id: 'user2',\n    status: 'published',\n    published_at: '2024-12-06T16:00:00Z',\n    created_at: '2024-12-06T15:00:00Z',\n    updated_at: '2024-12-06T16:00:00Z',\n    view_count: 178,\n    tags: ['云计算', '升级', '性能'],\n    category: mockCategories[2],\n    author: mockUsers[1],\n  },\n];\n\n// 模拟 API 延迟\nconst delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));\n\n// 模拟新闻 API\nexport const mockNewsApi = {\n  async getNews(filters: NewsFilters = {}) {\n    await delay(500); // 模拟网络延迟\n    \n    let filteredNews = [...mockNews];\n    \n    if (filters.category_id) {\n      filteredNews = filteredNews.filter(news => news.category_id === filters.category_id);\n    }\n    \n    if (filters.status) {\n      filteredNews = filteredNews.filter(news => news.status === filters.status);\n    }\n    \n    if (filters.search) {\n      const searchLower = filters.search.toLowerCase();\n      filteredNews = filteredNews.filter(news => \n        news.title.toLowerCase().includes(searchLower) ||\n        news.content.toLowerCase().includes(searchLower)\n      );\n    }\n    \n    // 简单分页\n    const limit = filters.limit || 10;\n    const offset = ((filters.page || 1) - 1) * limit;\n    \n    return filteredNews.slice(offset, offset + limit);\n  },\n\n  async getNewsById(id: string) {\n    await delay(300);\n    const news = mockNews.find(n => n.id === id);\n    if (!news) throw new Error('新闻不存在');\n    return news;\n  },\n\n  async createNews(newsData: CreateNewsData) {\n    await delay(800);\n\n    // 根据用户角色决定新闻状态\n    let finalStatus = newsData.status;\n    if (currentUser?.role === 'editor' && newsData.status === 'published') {\n      finalStatus = 'pending'; // 编辑提交发布申请，状态改为待审批\n    }\n\n    const newNews: NewsWithDetails = {\n      id: `news${Date.now()}`,\n      ...newsData,\n      status: finalStatus,\n      author_id: currentUser?.id || 'user1',\n      view_count: 0,\n      published_at: finalStatus === 'published' ? new Date().toISOString() : undefined,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n      category: mockCategories.find(c => c.id === newsData.category_id) || mockCategories[0],\n      author: currentUser || mockUsers[0],\n    };\n    mockNews.unshift(newNews);\n    return newNews;\n  },\n\n  async updateNews(newsData: UpdateNewsData) {\n    await delay(800);\n    const index = mockNews.findIndex(n => n.id === newsData.id);\n    if (index === -1) throw new Error('新闻不存在');\n    \n    const { id, ...updateData } = newsData;\n    mockNews[index] = {\n      ...mockNews[index],\n      ...updateData,\n      updated_at: new Date().toISOString(),\n      published_at: updateData.status === 'published' ? new Date().toISOString() : mockNews[index].published_at,\n      category: updateData.category_id ? \n        mockCategories.find(c => c.id === updateData.category_id) || mockNews[index].category :\n        mockNews[index].category,\n    };\n    return mockNews[index];\n  },\n\n  async deleteNews(id: string) {\n    await delay(500);\n    const index = mockNews.findIndex(n => n.id === id);\n    if (index === -1) throw new Error('新闻不存在');\n    mockNews.splice(index, 1);\n  },\n\n  async incrementViewCount(id: string) {\n    await delay(200);\n    const news = mockNews.find(n => n.id === id);\n    if (news) {\n      news.view_count += 1;\n    }\n  },\n\n  async approveNews(newsId: string) {\n    await delay(500);\n    const index = mockNews.findIndex(n => n.id === newsId);\n    if (index === -1) throw new Error('新闻不存在');\n\n    mockNews[index] = {\n      ...mockNews[index],\n      status: 'published',\n      published_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    };\n    return mockNews[index];\n  },\n\n  async rejectNews(newsId: string, reason: string) {\n    await delay(500);\n    const index = mockNews.findIndex(n => n.id === newsId);\n    if (index === -1) throw new Error('新闻不存在');\n\n    mockNews[index] = {\n      ...mockNews[index],\n      status: 'rejected',\n      rejection_reason: reason,\n      updated_at: new Date().toISOString(),\n    };\n    return mockNews[index];\n  },\n};\n\n// 模拟分类 API\nexport const mockCategoryApi = {\n  async getCategories() {\n    await delay(300);\n    return [...mockCategories];\n  },\n\n  async createCategory(categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>) {\n    await delay(500);\n    const newCategory: Category = {\n      id: `cat${Date.now()}`,\n      ...categoryData,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    };\n    mockCategories.push(newCategory);\n    return newCategory;\n  },\n\n  async updateCategory(id: string, categoryData: Partial<Category>) {\n    await delay(500);\n    const index = mockCategories.findIndex(c => c.id === id);\n    if (index === -1) throw new Error('分类不存在');\n    \n    mockCategories[index] = {\n      ...mockCategories[index],\n      ...categoryData,\n      updated_at: new Date().toISOString(),\n    };\n    return mockCategories[index];\n  },\n\n  async deleteCategory(id: string) {\n    await delay(500);\n    const index = mockCategories.findIndex(c => c.id === id);\n    if (index === -1) throw new Error('分类不存在');\n    mockCategories.splice(index, 1);\n  },\n};\n\n// 当前登录用户（模拟会话）\nlet currentUser: User | null = null;\n\n// 导出获取当前用户的函数（用于调试）\nexport const getCurrentUser = () => currentUser;\n\n// 模拟用户 API\nexport const mockUserApi = {\n  async getCurrentUser() {\n    await delay(200);\n    return currentUser;\n  },\n\n  async signIn(email: string, password: string) {\n    await delay(1000);\n    const user = mockUsers.find(u => u.email === email && u.is_active !== false);\n    if (!user) throw new Error('用户不存在或已被禁用');\n\n    const storedPassword = mockPasswords[email];\n    if (!storedPassword || storedPassword !== password) {\n      throw new Error('密码错误');\n    }\n\n    currentUser = user;\n    return { user };\n  },\n\n  async signOut() {\n    await delay(300);\n    currentUser = null;\n  },\n\n  async getAllUsers() {\n    await delay(500);\n    return [...mockUsers];\n  },\n\n  async createUser(userData: { email: string; name: string; password: string; role: 'admin' | 'editor' }) {\n    await delay(800);\n\n    // 检查邮箱是否已存在\n    if (mockUsers.find(u => u.email === userData.email)) {\n      throw new Error('邮箱已存在');\n    }\n\n    const newUser: User = {\n      id: `user${Date.now()}`,\n      email: userData.email,\n      name: userData.name,\n      role: userData.role,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n      is_active: true,\n    };\n\n    mockUsers.push(newUser);\n    mockPasswords[userData.email] = userData.password;\n\n    return newUser;\n  },\n\n  async updateUser(userData: { id: string; name?: string; role?: 'admin' | 'editor'; is_active?: boolean }) {\n    await delay(500);\n\n    const index = mockUsers.findIndex(u => u.id === userData.id);\n    if (index === -1) throw new Error('用户不存在');\n\n    mockUsers[index] = {\n      ...mockUsers[index],\n      ...userData,\n      updated_at: new Date().toISOString(),\n    };\n\n    return mockUsers[index];\n  },\n\n  async deleteUser(userId: string) {\n    await delay(500);\n\n    const index = mockUsers.findIndex(u => u.id === userId);\n    if (index === -1) throw new Error('用户不存在');\n\n    const user = mockUsers[index];\n    mockUsers.splice(index, 1);\n    delete mockPasswords[user.email];\n  },\n};\n"], "names": [], "mappings": ";;;;;;AAEA,OAAO;AACP,MAAM,iBAA6B;IACjC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,YAAY;QACZ,YAAY;IACd;CACD;AAED,IAAI,YAAoB;IACtB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,WAAW;IACb;CACD;AAED,sBAAsB;AACtB,MAAM,gBAAwC;IAC5C,sBAAsB;IACtB,uBAAuB;IACvB,wBAAwB;AAC1B;AAEA,IAAI,WAA8B;IAChC;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,aAAa;QACb,WAAW;QACX,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAQ;YAAM;SAAK;QAC1B,UAAU,cAAc,CAAC,EAAE;QAC3B,QAAQ,SAAS,CAAC,EAAE;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,aAAa;QACb,WAAW;QACX,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAQ;YAAQ;SAAK;QAC5B,UAAU,cAAc,CAAC,EAAE;QAC3B,QAAQ,SAAS,CAAC,EAAE;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,aAAa;QACb,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAQ;YAAQ;SAAK;QAC5B,UAAU,cAAc,CAAC,EAAE;QAC3B,QAAQ,SAAS,CAAC,EAAE;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,aAAa;QACb,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAO;SAAK;QACzB,UAAU,cAAc,CAAC,EAAE;QAC3B,QAAQ,SAAS,CAAC,EAAE;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,aAAa;QACb,WAAW;QACX,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAS;YAAQ;SAAK;QAC7B,UAAU,cAAc,CAAC,EAAE;QAC3B,QAAQ,SAAS,CAAC,EAAE;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,aAAa;QACb,WAAW;QACX,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAM;YAAM;SAAO;QAC1B,UAAU,cAAc,CAAC,EAAE;QAC3B,QAAQ,SAAS,CAAC,EAAE;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;QACT,aAAa;QACb,WAAW;QACX,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,MAAM;YAAC;YAAO;YAAM;SAAK;QACzB,UAAU,cAAc,CAAC,EAAE;QAC3B,QAAQ,SAAS,CAAC,EAAE;IACtB;CACD;AAED,YAAY;AACZ,MAAM,QAAQ,CAAC,KAAe,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AAGlE,MAAM,cAAc;IACzB,MAAM,SAAQ,UAAuB,CAAC,CAAC;QACrC,MAAM,MAAM,MAAM,SAAS;QAE3B,IAAI,eAAe;eAAI;SAAS;QAEhC,IAAI,QAAQ,WAAW,EAAE;YACvB,eAAe,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK,QAAQ,WAAW;QACrF;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,eAAe,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,MAAM;QAC3E;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;YAC9C,eAAe,aAAa,MAAM,CAAC,CAAA,OACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;QAExC;QAEA,OAAO;QACP,MAAM,QAAQ,QAAQ,KAAK,IAAI;QAC/B,MAAM,SAAS,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;QAE3C,OAAO,aAAa,KAAK,CAAC,QAAQ,SAAS;IAC7C;IAEA,MAAM,aAAY,EAAU;QAC1B,MAAM,MAAM;QACZ,MAAM,OAAO,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzC,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAC3B,OAAO;IACT;IAEA,MAAM,YAAW,QAAwB;QACvC,MAAM,MAAM;QAEZ,eAAe;QACf,IAAI,cAAc,SAAS,MAAM;QACjC,IAAI,aAAa,SAAS,YAAY,SAAS,MAAM,KAAK,aAAa;YACrE,cAAc,WAAW,mBAAmB;QAC9C;QAEA,MAAM,UAA2B;YAC/B,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;YACvB,GAAG,QAAQ;YACX,QAAQ;YACR,WAAW,aAAa,MAAM;YAC9B,YAAY;YACZ,cAAc,gBAAgB,cAAc,IAAI,OAAO,WAAW,KAAK;YACvE,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;YAClC,UAAU,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,WAAW,KAAK,cAAc,CAAC,EAAE;YACtF,QAAQ,eAAe,SAAS,CAAC,EAAE;QACrC;QACA,SAAS,OAAO,CAAC;QACjB,OAAO;IACT;IAEA,MAAM,YAAW,QAAwB;QACvC,MAAM,MAAM;QACZ,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE;QAC1D,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM;QAElC,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;QAC9B,QAAQ,CAAC,MAAM,GAAG;YAChB,GAAG,QAAQ,CAAC,MAAM;YAClB,GAAG,UAAU;YACb,YAAY,IAAI,OAAO,WAAW;YAClC,cAAc,WAAW,MAAM,KAAK,cAAc,IAAI,OAAO,WAAW,KAAK,QAAQ,CAAC,MAAM,CAAC,YAAY;YACzG,UAAU,WAAW,WAAW,GAC9B,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,WAAW,KAAK,QAAQ,CAAC,MAAM,CAAC,QAAQ,GACrF,QAAQ,CAAC,MAAM,CAAC,QAAQ;QAC5B;QACA,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,MAAM,YAAW,EAAU;QACzB,MAAM,MAAM;QACZ,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM;QAClC,SAAS,MAAM,CAAC,OAAO;IACzB;IAEA,MAAM,oBAAmB,EAAU;QACjC,MAAM,MAAM;QACZ,MAAM,OAAO,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzC,IAAI,MAAM;YACR,KAAK,UAAU,IAAI;QACrB;IACF;IAEA,MAAM,aAAY,MAAc;QAC9B,MAAM,MAAM;QACZ,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM;QAElC,QAAQ,CAAC,MAAM,GAAG;YAChB,GAAG,QAAQ,CAAC,MAAM;YAClB,QAAQ;YACR,cAAc,IAAI,OAAO,WAAW;YACpC,YAAY,IAAI,OAAO,WAAW;QACpC;QACA,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,MAAM,YAAW,MAAc,EAAE,MAAc;QAC7C,MAAM,MAAM;QACZ,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM;QAElC,QAAQ,CAAC,MAAM,GAAG;YAChB,GAAG,QAAQ,CAAC,MAAM;YAClB,QAAQ;YACR,kBAAkB;YAClB,YAAY,IAAI,OAAO,WAAW;QACpC;QACA,OAAO,QAAQ,CAAC,MAAM;IACxB;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM;QACJ,MAAM,MAAM;QACZ,OAAO;eAAI;SAAe;IAC5B;IAEA,MAAM,gBAAe,YAAgE;QACnF,MAAM,MAAM;QACZ,MAAM,cAAwB;YAC5B,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI;YACtB,GAAG,YAAY;YACf,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QACA,eAAe,IAAI,CAAC;QACpB,OAAO;IACT;IAEA,MAAM,gBAAe,EAAU,EAAE,YAA+B;QAC9D,MAAM,MAAM;QACZ,MAAM,QAAQ,eAAe,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM;QAElC,cAAc,CAAC,MAAM,GAAG;YACtB,GAAG,cAAc,CAAC,MAAM;YACxB,GAAG,YAAY;YACf,YAAY,IAAI,OAAO,WAAW;QACpC;QACA,OAAO,cAAc,CAAC,MAAM;IAC9B;IAEA,MAAM,gBAAe,EAAU;QAC7B,MAAM,MAAM;QACZ,MAAM,QAAQ,eAAe,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM;QAClC,eAAe,MAAM,CAAC,OAAO;IAC/B;AACF;AAEA,eAAe;AACf,IAAI,cAA2B;AAGxB,MAAM,iBAAiB,IAAM;AAG7B,MAAM,cAAc;IACzB,MAAM;QACJ,MAAM,MAAM;QACZ,OAAO;IACT;IAEA,MAAM,QAAO,KAAa,EAAE,QAAgB;QAC1C,MAAM,MAAM;QACZ,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,EAAE,SAAS,KAAK;QACtE,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,MAAM,iBAAiB,aAAa,CAAC,MAAM;QAC3C,IAAI,CAAC,kBAAkB,mBAAmB,UAAU;YAClD,MAAM,IAAI,MAAM;QAClB;QAEA,cAAc;QACd,OAAO;YAAE;QAAK;IAChB;IAEA,MAAM;QACJ,MAAM,MAAM;QACZ,cAAc;IAChB;IAEA,MAAM;QACJ,MAAM,MAAM;QACZ,OAAO;eAAI;SAAU;IACvB;IAEA,MAAM,YAAW,QAAqF;QACpG,MAAM,MAAM;QAEZ,YAAY;QACZ,IAAI,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,KAAK,GAAG;YACnD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,UAAgB;YACpB,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;YACvB,OAAO,SAAS,KAAK;YACrB,MAAM,SAAS,IAAI;YACnB,MAAM,SAAS,IAAI;YACnB,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;YAClC,WAAW;QACb;QAEA,UAAU,IAAI,CAAC;QACf,aAAa,CAAC,SAAS,KAAK,CAAC,GAAG,SAAS,QAAQ;QAEjD,OAAO;IACT;IAEA,MAAM,YAAW,QAAuF;QACtG,MAAM,MAAM;QAEZ,MAAM,QAAQ,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE;QAC3D,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM;QAElC,SAAS,CAAC,MAAM,GAAG;YACjB,GAAG,SAAS,CAAC,MAAM;YACnB,GAAG,QAAQ;YACX,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,OAAO,SAAS,CAAC,MAAM;IACzB;IAEA,MAAM,YAAW,MAAc;QAC7B,MAAM,MAAM;QAEZ,MAAM,QAAQ,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM;QAElC,MAAM,OAAO,SAAS,CAAC,MAAM;QAC7B,UAAU,MAAM,CAAC,OAAO;QACxB,OAAO,aAAa,CAAC,KAAK,KAAK,CAAC;IAClC;AACF", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/lib/api.ts"], "sourcesContent": ["import { supabase, TABLES } from './supabase';\nimport { mockNews<PERSON>pi, mockCategory<PERSON>pi, mockUser<PERSON>pi } from './mockData';\nimport type { News, Category, User, CreateNewsData, UpdateNewsData, NewsFilters, NewsWithDetails, CreateUserData, UpdateUserData } from '@/types';\n\n// 检查是否有有效的 Supabase 配置\nconst isSupabaseConfigured = () => {\n  // 如果是演示模式，直接使用模拟数据\n  if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {\n    return false;\n  }\n\n  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;\n  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n  return url && key && url !== 'your_supabase_url_here' && key !== 'your_supabase_anon_key_here';\n};\n\n// 新闻相关 API\nexport const newsApi = {\n  // 获取新闻列表\n  async getNews(filters: NewsFilters = {}) {\n    if (!isSupabaseConfigured()) {\n      return mockNewsApi.getNews(filters);\n    }\n\n    let query = supabase\n      .from(TABLES.NEWS)\n      .select(`\n        *,\n        category:categories(*),\n        author:users(*)\n      `)\n      .order('created_at', { ascending: false });\n\n    if (filters.category_id) {\n      query = query.eq('category_id', filters.category_id);\n    }\n\n    if (filters.status) {\n      query = query.eq('status', filters.status);\n    }\n\n    if (filters.search) {\n      query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`);\n    }\n\n    const limit = filters.limit || 10;\n    const offset = ((filters.page || 1) - 1) * limit;\n\n    query = query.range(offset, offset + limit - 1);\n\n    const { data, error } = await query;\n\n    if (error) throw error;\n    return data as NewsWithDetails[];\n  },\n\n  // 获取单个新闻\n  async getNewsById(id: string) {\n    if (!isSupabaseConfigured()) {\n      return mockNewsApi.getNewsById(id);\n    }\n\n    const { data, error } = await supabase\n      .from(TABLES.NEWS)\n      .select(`\n        *,\n        category:categories(*),\n        author:users(*)\n      `)\n      .eq('id', id)\n      .single();\n\n    if (error) throw error;\n    return data as NewsWithDetails;\n  },\n\n  // 创建新闻\n  async createNews(newsData: CreateNewsData) {\n    if (!isSupabaseConfigured()) {\n      return mockNewsApi.createNews(newsData);\n    }\n\n    const { data, error } = await supabase\n      .from(TABLES.NEWS)\n      .insert([{\n        ...newsData,\n        author_id: 'current_user_id', // 这里需要从认证系统获取当前用户ID\n        view_count: 0,\n        published_at: newsData.status === 'published' ? new Date().toISOString() : null,\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as News;\n  },\n\n  // 更新新闻\n  async updateNews(newsData: UpdateNewsData) {\n    if (!isSupabaseConfigured()) {\n      return mockNewsApi.updateNews(newsData);\n    }\n\n    const { id, ...updateData } = newsData;\n\n    const { data, error } = await supabase\n      .from(TABLES.NEWS)\n      .update({\n        ...updateData,\n        updated_at: new Date().toISOString(),\n        published_at: updateData.status === 'published' ? new Date().toISOString() : undefined,\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as News;\n  },\n\n  // 删除新闻\n  async deleteNews(id: string) {\n    if (!isSupabaseConfigured()) {\n      return mockNewsApi.deleteNews(id);\n    }\n\n    const { error } = await supabase\n      .from(TABLES.NEWS)\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  },\n\n  // 增加浏览量\n  async incrementViewCount(id: string) {\n    if (!isSupabaseConfigured()) {\n      return mockNewsApi.incrementViewCount(id);\n    }\n\n    const { error } = await supabase.rpc('increment_view_count', { news_id: id });\n    if (error) throw error;\n  },\n\n  // 批准新闻\n  async approveNews(newsId: string) {\n    if (!isSupabaseConfigured()) {\n      return mockNewsApi.approveNews(newsId);\n    }\n\n    const { data, error } = await supabase\n      .from(TABLES.NEWS)\n      .update({\n        status: 'published',\n        published_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      })\n      .eq('id', newsId)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as News;\n  },\n\n  // 拒绝新闻\n  async rejectNews(newsId: string, reason: string) {\n    if (!isSupabaseConfigured()) {\n      return mockNewsApi.rejectNews(newsId, reason);\n    }\n\n    const { data, error } = await supabase\n      .from(TABLES.NEWS)\n      .update({\n        status: 'rejected',\n        rejection_reason: reason,\n        updated_at: new Date().toISOString(),\n      })\n      .eq('id', newsId)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as News;\n  },\n};\n\n// 分类相关 API\nexport const categoryApi = {\n  // 获取所有分类\n  async getCategories() {\n    if (!isSupabaseConfigured()) {\n      return mockCategoryApi.getCategories();\n    }\n\n    const { data, error } = await supabase\n      .from(TABLES.CATEGORIES)\n      .select('*')\n      .order('name');\n\n    if (error) throw error;\n    return data as Category[];\n  },\n\n  // 创建分类\n  async createCategory(categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>) {\n    if (!isSupabaseConfigured()) {\n      return mockCategoryApi.createCategory(categoryData);\n    }\n\n    const { data, error } = await supabase\n      .from(TABLES.CATEGORIES)\n      .insert([categoryData])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as Category;\n  },\n\n  // 更新分类\n  async updateCategory(id: string, categoryData: Partial<Category>) {\n    if (!isSupabaseConfigured()) {\n      return mockCategoryApi.updateCategory(id, categoryData);\n    }\n\n    const { data, error } = await supabase\n      .from(TABLES.CATEGORIES)\n      .update({\n        ...categoryData,\n        updated_at: new Date().toISOString(),\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as Category;\n  },\n\n  // 删除分类\n  async deleteCategory(id: string) {\n    if (!isSupabaseConfigured()) {\n      return mockCategoryApi.deleteCategory(id);\n    }\n\n    const { error } = await supabase\n      .from(TABLES.CATEGORIES)\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  },\n};\n\n// 用户相关 API\nexport const userApi = {\n  // 获取当前用户\n  async getCurrentUser() {\n    if (!isSupabaseConfigured()) {\n      return mockUserApi.getCurrentUser();\n    }\n\n    const { data: { user } } = await supabase.auth.getUser();\n    return user;\n  },\n\n  // 登录\n  async signIn(email: string, password: string) {\n    if (!isSupabaseConfigured()) {\n      return mockUserApi.signIn(email, password);\n    }\n\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    });\n\n    if (error) throw error;\n    return data;\n  },\n\n  // 登出\n  async signOut() {\n    if (!isSupabaseConfigured()) {\n      return mockUserApi.signOut();\n    }\n\n    const { error } = await supabase.auth.signOut();\n    if (error) throw error;\n  },\n\n  // 获取所有用户（管理员功能）\n  async getAllUsers() {\n    if (!isSupabaseConfigured()) {\n      return mockUserApi.getAllUsers();\n    }\n\n    const { data, error } = await supabase\n      .from(TABLES.USERS)\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) throw error;\n    return data as User[];\n  },\n\n  // 创建用户（管理员功能）\n  async createUser(userData: CreateUserData) {\n    if (!isSupabaseConfigured()) {\n      return mockUserApi.createUser(userData);\n    }\n\n    // 在实际的 Supabase 实现中，这里需要使用 Supabase Auth Admin API\n    // 或者通过服务端 API 来创建用户\n    const { data, error } = await supabase\n      .from(TABLES.USERS)\n      .insert([{\n        email: userData.email,\n        name: userData.name,\n        role: userData.role,\n        is_active: true,\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as User;\n  },\n\n  // 更新用户（管理员功能）\n  async updateUser(userData: UpdateUserData) {\n    if (!isSupabaseConfigured()) {\n      return mockUserApi.updateUser(userData);\n    }\n\n    const { id, ...updateData } = userData;\n\n    const { data, error } = await supabase\n      .from(TABLES.USERS)\n      .update({\n        ...updateData,\n        updated_at: new Date().toISOString(),\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as User;\n  },\n\n  // 删除用户（管理员功能）\n  async deleteUser(userId: string) {\n    if (!isSupabaseConfigured()) {\n      return mockUserApi.deleteUser(userId);\n    }\n\n    const { error } = await supabase\n      .from(TABLES.USERS)\n      .delete()\n      .eq('id', userId);\n\n    if (error) throw error;\n  },\n};\n"], "names": [], "mappings": ";;;;;AAOM;AAPN;AACA;;;AAGA,uBAAuB;AACvB,MAAM,uBAAuB;IAC3B,mBAAmB;IACnB,wCAAkD;QAChD,OAAO;IACT;;IAEA,MAAM;IACN,MAAM;AAER;AAGO,MAAM,UAAU;IACrB,SAAS;IACT,MAAM,SAAQ,UAAuB,CAAC,CAAC;QACrC,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,OAAO,CAAC;QAC7B;QAEA,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,WAAW,EAAE;YACvB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;QACrD;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,QAAQ,MAAM,EAAE,CAAC,UAAU,QAAQ,MAAM;QAC3C;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,QAAQ,MAAM,EAAE,CAAC,CAAC,aAAa,EAAE,QAAQ,MAAM,CAAC,iBAAiB,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;QACtF;QAEA,MAAM,QAAQ,QAAQ,KAAK,IAAI;QAC/B,MAAM,SAAS,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;QAE3C,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAE7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,SAAS;IACT,MAAM,aAAY,EAAU;QAC1B,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,WAAW,CAAC;QACjC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,YAAW,QAAwB;QACvC,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAChC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,CAAC;YAAC;gBACP,GAAG,QAAQ;gBACX,WAAW;gBACX,YAAY;gBACZ,cAAc,SAAS,MAAM,KAAK,cAAc,IAAI,OAAO,WAAW,KAAK;YAC7E;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,YAAW,QAAwB;QACvC,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAChC;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;QAE9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,CAAC;YACN,GAAG,UAAU;YACb,YAAY,IAAI,OAAO,WAAW;YAClC,cAAc,WAAW,MAAM,KAAK,cAAc,IAAI,OAAO,WAAW,KAAK;QAC/E,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,YAAW,EAAU;QACzB,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAChC;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,QAAQ;IACR,MAAM,oBAAmB,EAAU;QACjC,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,kBAAkB,CAAC;QACxC;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,wBAAwB;YAAE,SAAS;QAAG;QAC3E,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;IACP,MAAM,aAAY,MAAc;QAC9B,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,WAAW,CAAC;QACjC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,CAAC;YACN,QAAQ;YACR,cAAc,IAAI,OAAO,WAAW;YACpC,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,YAAW,MAAc,EAAE,MAAc;QAC7C,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,UAAU,CAAC,QAAQ;QACxC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,CAAC;YACN,QAAQ;YACR,kBAAkB;YAClB,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,SAAS;IACT,MAAM;QACJ,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,kBAAe,CAAC,aAAa;QACtC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,UAAU,EACtB,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,gBAAe,YAAgE;QACnF,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;QACxC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,UAAU,EACtB,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,gBAAe,EAAU,EAAE,YAA+B;QAC9D,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,IAAI;QAC5C;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,UAAU,EACtB,MAAM,CAAC;YACN,GAAG,YAAY;YACf,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,gBAAe,EAAU;QAC7B,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;QACxC;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,UAAU,EACtB,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM,UAAU;IACrB,SAAS;IACT,MAAM;QACJ,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,cAAc;QACnC;QAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QACtD,OAAO;IACT;IAEA,KAAK;IACL,MAAM,QAAO,KAAa,EAAE,QAAgB;QAC1C,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,MAAM,CAAC,OAAO;QACnC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,KAAK;IACL,MAAM;QACJ,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,OAAO;QAC5B;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;IAEA,gBAAgB;IAChB,MAAM;QACJ,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,WAAW;QAChC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,cAAc;IACd,MAAM,YAAW,QAAwB;QACvC,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAChC;QAEA,mDAAmD;QACnD,oBAAoB;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC;YAAC;gBACP,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI;gBACnB,MAAM,SAAS,IAAI;gBACnB,WAAW;YACb;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,cAAc;IACd,MAAM,YAAW,QAAwB;QACvC,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAChC;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;QAE9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,CAAC;YACN,GAAG,UAAU;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,cAAc;IACd,MAAM,YAAW,MAAc;QAC7B,IAAI,CAAC,wBAAwB;YAC3B,OAAO,yHAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAChC;QAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,KAAK,EACjB,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;AACF", "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { userApi } from '@/lib/api';\nimport type { User, AuthContextType } from '@/types';\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    checkUser();\n  }, []);\n\n  const checkUser = async () => {\n    try {\n      const currentUser = await userApi.getCurrentUser();\n      setUser(currentUser);\n    } catch (error) {\n      console.error('获取用户信息失败:', error);\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await userApi.signIn(email, password);\n      if (response.user) {\n        setUser(response.user);\n      }\n    } catch (error) {\n      console.error('登录失败:', error);\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await userApi.signOut();\n      setUser(null);\n    } catch (error) {\n      console.error('登出失败:', error);\n      throw error;\n    }\n  };\n\n  const isAdmin = () => {\n    return user?.role === 'admin';\n  };\n\n  const isEditor = () => {\n    return user?.role === 'editor';\n  };\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    login,\n    logout,\n    isAdmin,\n    isEditor,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAMA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,cAAc,MAAM,oHAAA,CAAA,UAAO,CAAC,cAAc;YAChD,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM,CAAC,OAAO;YAC7C,IAAI,SAAS,IAAI,EAAE;gBACjB,QAAQ,SAAS,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,OAAO;YACrB,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,MAAM;QACR;IACF;IAEA,MAAM,UAAU;QACd,OAAO,MAAM,SAAS;IACxB;IAEA,MAAM,WAAW;QACf,OAAO,MAAM,SAAS;IACxB;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAhEgB;KAAA;AAkET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}