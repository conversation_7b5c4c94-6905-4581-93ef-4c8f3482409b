{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/LoginForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\nimport { useAuth } from '@/contexts/AuthContext';\n\nconst loginSchema = z.object({\n  email: z.string().email('请输入有效的邮箱地址'),\n  password: z.string().min(1, '密码不能为空'),\n});\n\ntype LoginFormData = z.infer<typeof loginSchema>;\n\nexport default function LoginForm() {\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const { login } = useAuth();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<LoginFormData>({\n    resolver: zod<PERSON><PERSON>olver(loginSchema),\n  });\n\n  const onSubmit = async (data: LoginFormData) => {\n    try {\n      setIsLoading(true);\n      setError(null);\n      await login(data.email, data.password);\n    } catch (error: any) {\n      setError(error.message || '登录失败，请检查邮箱和密码');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            登录到新闻发布系统\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            请使用您的账户登录\n          </p>\n        </div>\n\n        {/* 演示账户信息 */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-md p-4\">\n          <h3 className=\"text-sm font-medium text-blue-800 mb-2\">演示账户</h3>\n          <div className=\"text-sm text-blue-700 space-y-1\">\n            <p><strong>管理员:</strong> <EMAIL> / admin123</p>\n            <p><strong>编辑:</strong> <EMAIL> / editor123</p>\n          </div>\n        </div>\n\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit(onSubmit)}>\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n              <p className=\"text-sm text-red-600\">{error}</p>\n            </div>\n          )}\n\n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                邮箱地址\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                {...register('email')}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"请输入邮箱地址\"\n              />\n              {errors.email && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                密码\n              </label>\n              <div className=\"mt-1 relative\">\n                <input\n                  id=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  {...register('password')}\n                  className=\"appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"请输入密码\"\n                />\n                <button\n                  type=\"button\"\n                  className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                  onClick={() => setShowPassword(!showPassword)}\n                >\n                  {showPassword ? (\n                    <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                  ) : (\n                    <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                  )}\n                </button>\n              </div>\n              {errors.password && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.password.message}</p>\n              )}\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  登录中...\n                </div>\n              ) : (\n                '登录'\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AASA,MAAM,cAAc,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAExB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,aAAa;YACb,SAAS;YACT,MAAM,MAAM,KAAK,KAAK,EAAE,KAAK,QAAQ;QACvC,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BAAE,WAAU;sCAAyC;;;;;;;;;;;;8BAMxD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAa;;;;;;;8CACxB,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;8BAI3B,6LAAC;oBAAK,WAAU;oBAAiB,UAAU,aAAa;;wBACrD,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;sCAIzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,cAAa;4CACZ,GAAG,SAAS,QAAQ;4CACrB,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAIlE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAM,eAAe,SAAS;oDAC9B,cAAa;oDACZ,GAAG,SAAS,WAAW;oDACxB,WAAU;oDACV,aAAY;;;;;;8DAEd,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,gBAAgB,CAAC;8DAE/B,6BACC,6LAAC,0NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;6EAExB,6LAAC,gNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAIxB,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAKvE,6LAAC;sCACC,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,0BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;wCAAuE;;;;;;2CAIxF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GA3HwB;;QAIJ,kIAAA,CAAA,UAAO;QAMrB,iKAAA,CAAA,UAAO;;;KAVW", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  NewspaperIcon, \n  UserGroupIcon, \n  CheckCircleIcon, \n  Bars3Icon, \n  XMarkIcon,\n  ArrowRightOnRectangleIcon \n} from '@heroicons/react/24/outline';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface NavigationProps {\n  currentView: string;\n  onViewChange: (view: string) => void;\n}\n\nexport default function Navigation({ currentView, onViewChange }: NavigationProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const { user, logout, isAdmin } = useAuth();\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('登出失败:', error);\n    }\n  };\n\n  const navigationItems = [\n    {\n      id: 'news',\n      name: '新闻管理',\n      icon: NewspaperIcon,\n      description: '管理新闻内容',\n    },\n    ...(isAdmin() ? [\n      {\n        id: 'approval',\n        name: '新闻审批',\n        icon: CheckCircleIcon,\n        description: '审批待发布新闻',\n      },\n      {\n        id: 'users',\n        name: '用户管理',\n        icon: UserGroupIcon,\n        description: '管理系统用户',\n      },\n    ] : []),\n  ];\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          {/* 左侧 - Logo 和导航 */}\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <h1 className=\"text-xl font-bold text-gray-900\">\n                新闻发布系统\n              </h1>\n            </div>\n            \n            {/* 桌面端导航 */}\n            <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n              {navigationItems.map((item) => {\n                const Icon = item.icon;\n                return (\n                  <button\n                    key={item.id}\n                    onClick={() => onViewChange(item.id)}\n                    className={`${\n                      currentView === item.id\n                        ? 'border-blue-500 text-gray-900'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm inline-flex items-center`}\n                  >\n                    <Icon className=\"h-4 w-4 mr-2\" />\n                    {item.name}\n                  </button>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* 右侧 - 用户信息和登出 */}\n          <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-sm\">\n                <span className=\"text-gray-500\">欢迎，</span>\n                <span className=\"font-medium text-gray-900\">{user?.name}</span>\n                <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                  user?.role === 'admin' \n                    ? 'bg-purple-100 text-purple-800' \n                    : 'bg-blue-100 text-blue-800'\n                }`}>\n                  {user?.role === 'admin' ? '管理员' : '编辑'}\n                </span>\n              </div>\n              <button\n                onClick={handleLogout}\n                className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-1\" />\n                登出\n              </button>\n            </div>\n          </div>\n\n          {/* 移动端菜单按钮 */}\n          <div className=\"sm:hidden flex items-center\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              {isMobileMenuOpen ? (\n                <XMarkIcon className=\"block h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"block h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 移动端菜单 */}\n      {isMobileMenuOpen && (\n        <div className=\"sm:hidden\">\n          <div className=\"pt-2 pb-3 space-y-1\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon;\n              return (\n                <button\n                  key={item.id}\n                  onClick={() => {\n                    onViewChange(item.id);\n                    setIsMobileMenuOpen(false);\n                  }}\n                  className={`${\n                    currentView === item.id\n                      ? 'bg-blue-50 border-blue-500 text-blue-700'\n                      : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'\n                  } block pl-3 pr-4 py-2 border-l-4 text-base font-medium w-full text-left`}\n                >\n                  <div className=\"flex items-center\">\n                    <Icon className=\"h-5 w-5 mr-3\" />\n                    <div>\n                      <div>{item.name}</div>\n                      <div className=\"text-xs text-gray-500\">{item.description}</div>\n                    </div>\n                  </div>\n                </button>\n              );\n            })}\n          </div>\n          \n          {/* 移动端用户信息 */}\n          <div className=\"pt-4 pb-3 border-t border-gray-200\">\n            <div className=\"flex items-center px-4\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-gray-700\">\n                    {user?.name?.charAt(0)}\n                  </span>\n                </div>\n              </div>\n              <div className=\"ml-3\">\n                <div className=\"text-base font-medium text-gray-800\">{user?.name}</div>\n                <div className=\"text-sm text-gray-500\">{user?.email}</div>\n              </div>\n            </div>\n            <div className=\"mt-3 space-y-1\">\n              <button\n                onClick={handleLogout}\n                className=\"block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 w-full text-left\"\n              >\n                <div className=\"flex items-center\">\n                  <ArrowRightOnRectangleIcon className=\"h-5 w-5 mr-3\" />\n                  登出\n                </div>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAXA;;;;AAkBe,SAAS,WAAW,EAAE,WAAW,EAAE,YAAY,EAAmB;;IAC/E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAExC,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,MAAM;YACN,MAAM,4NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;WACI,YAAY;YACd;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM,gOAAA,CAAA,kBAAe;gBACrB,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM,4NAAA,CAAA,gBAAa;gBACnB,aAAa;YACf;SACD,GAAG,EAAE;KACP;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;;;;;;8CAMlD,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC;wCACpB,MAAM,OAAO,KAAK,IAAI;wCACtB,qBACE,6LAAC;4CAEC,SAAS,IAAM,aAAa,KAAK,EAAE;4CACnC,WAAW,GACT,gBAAgB,KAAK,EAAE,GACnB,kCACA,6EACL,oFAAoF,CAAC;;8DAEtF,6LAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;2CATL,KAAK,EAAE;;;;;oCAYlB;;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAA6B,MAAM;;;;;;0DACnD,6LAAC;gDAAK,WAAW,CAAC,6EAA6E,EAC7F,MAAM,SAAS,UACX,kCACA,6BACJ;0DACC,MAAM,SAAS,UAAU,QAAQ;;;;;;;;;;;;kDAGtC,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,oPAAA,CAAA,4BAAyB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;sCAO5D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAET,iCACC,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9B,kCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC;4BACpB,MAAM,OAAO,KAAK,IAAI;4BACtB,qBACE,6LAAC;gCAEC,SAAS;oCACP,aAAa,KAAK,EAAE;oCACpB,oBAAoB;gCACtB;gCACA,WAAW,GACT,gBAAgB,KAAK,EAAE,GACnB,6CACA,8FACL,uEAAuE,CAAC;0CAEzE,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;;8DACC,6LAAC;8DAAK,KAAK,IAAI;;;;;;8DACf,6LAAC;oDAAI,WAAU;8DAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;+BAfvD,KAAK,EAAE;;;;;wBAoBlB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,MAAM,MAAM,OAAO;;;;;;;;;;;;;;;;kDAI1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAuC,MAAM;;;;;;0DAC5D,6LAAC;gDAAI,WAAU;0DAAyB,MAAM;;;;;;;;;;;;;;;;;;0CAGlD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oPAAA,CAAA,4BAAyB;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE;GA3KwB;;QAEY,kIAAA,CAAA,UAAO;;;KAFnB", "debugId": null}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/NewsList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { format } from 'date-fns';\nimport { zhCN } from 'date-fns/locale';\nimport { EyeIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';\nimport { newsApi, categoryApi } from '@/lib/api';\nimport type { NewsWithDetails, Category, NewsFilters } from '@/types';\n\ninterface NewsListProps {\n  onEdit: (news: NewsWithDetails) => void;\n  onDelete: (id: string) => void;\n  onView?: (news: NewsWithDetails) => void;\n  refreshTrigger?: number;\n}\n\nexport default function NewsList({ onEdit, onDelete, onView, refreshTrigger }: NewsListProps) {\n  const [news, setNews] = useState<NewsWithDetails[]>([]);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState<NewsFilters>({\n    page: 1,\n    limit: 10,\n  });\n\n  useEffect(() => {\n    loadData();\n  }, [filters, refreshTrigger]);\n\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const data = await newsApi.getNews(filters);\n      setNews(data);\n    } catch (error) {\n      console.error('加载新闻失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCategories = async () => {\n    try {\n      const data = await categoryApi.getCategories();\n      setCategories(data);\n    } catch (error) {\n      console.error('加载分类失败:', error);\n    }\n  };\n\n  const handleFilterChange = (key: keyof NewsFilters, value: string) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value || undefined,\n      page: 1, // 重置页码\n    }));\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      draft: { label: '草稿', className: 'bg-gray-100 text-gray-800' },\n      pending: { label: '待审批', className: 'bg-yellow-100 text-yellow-800' },\n      published: { label: '已发布', className: 'bg-green-100 text-green-800' },\n      rejected: { label: '已拒绝', className: 'bg-red-100 text-red-800' },\n      archived: { label: '已归档', className: 'bg-purple-100 text-purple-800' },\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;\n\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}>\n        {config.label}\n      </span>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white shadow-md rounded-lg overflow-hidden\">\n      {/* 筛选器 */}\n      <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n        <div className=\"flex flex-wrap gap-4\">\n          <div className=\"flex-1 min-w-64\">\n            <input\n              type=\"text\"\n              placeholder=\"搜索新闻标题或内容...\"\n              value={filters.search || ''}\n              onChange={(e) => handleFilterChange('search', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <div>\n            <select\n              value={filters.category_id || ''}\n              onChange={(e) => handleFilterChange('category_id', e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">所有分类</option>\n              {categories.map((category) => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n          </div>\n          <div>\n            <select\n              value={filters.status || ''}\n              onChange={(e) => handleFilterChange('status', e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">所有状态</option>\n              <option value=\"draft\">草稿</option>\n              <option value=\"pending\">待审批</option>\n              <option value=\"published\">已发布</option>\n              <option value=\"rejected\">已拒绝</option>\n              <option value=\"archived\">已归档</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* 新闻列表 */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                标题\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                分类\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                状态\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                浏览量\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                创建时间\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                操作\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {news.length === 0 ? (\n              <tr>\n                <td colSpan={6} className=\"px-6 py-12 text-center text-gray-500\">\n                  暂无新闻数据\n                </td>\n              </tr>\n            ) : (\n              news.map((item) => (\n                <tr key={item.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4\">\n                    <div className=\"max-w-xs\">\n                      <div\n                        className=\"text-sm font-medium text-blue-600 hover:text-blue-800 truncate cursor-pointer transition-colors duration-200\"\n                        onClick={() => onView?.(item)}\n                        title=\"点击查看新闻详情\"\n                      >\n                        {item.title}\n                      </div>\n                      <div className=\"text-sm text-gray-500 truncate\">\n                        {item.excerpt}\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                      {item.category.name}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {getStatusBadge(item.status)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center text-sm text-gray-500\">\n                      <EyeIcon className=\"h-4 w-4 mr-1\" />\n                      {item.view_count}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {format(new Date(item.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => onEdit(item)}\n                        className=\"text-blue-600 hover:text-blue-900\"\n                        title=\"编辑\"\n                      >\n                        <PencilIcon className=\"h-4 w-4\" />\n                      </button>\n                      <button\n                        onClick={() => onDelete(item.id)}\n                        className=\"text-red-600 hover:text-red-900\"\n                        title=\"删除\"\n                      >\n                        <TrashIcon className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAgBe,SAAS,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAiB;;IAC1F,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,MAAM;QACN,OAAO;IACT;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG;QAAC;QAAS;KAAe;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,oHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YACnC,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,OAAO,MAAM,oHAAA,CAAA,cAAW,CAAC,aAAa;YAC5C,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,qBAAqB,CAAC,KAAwB;QAClD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE,SAAS;gBAChB,MAAM;YACR,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,OAAO;gBAAE,OAAO;gBAAM,WAAW;YAA4B;YAC7D,SAAS;gBAAE,OAAO;gBAAO,WAAW;YAAgC;YACpE,WAAW;gBAAE,OAAO;gBAAO,WAAW;YAA8B;YACpE,UAAU;gBAAE,OAAO;gBAAO,WAAW;YAA0B;YAC/D,UAAU;gBAAE,OAAO;gBAAO,WAAW;YAAgC;QACvE;QAEA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,KAAK;QAEtF,qBACE,6LAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,OAAO,SAAS,EAAE;sBAC3G,OAAO,KAAK;;;;;;IAGnB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,QAAQ,MAAM,IAAI;gCACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;;;;;;;;;;;sCAGd,6LAAC;sCACC,cAAA,6LAAC;gCACC,OAAO,QAAQ,WAAW,IAAI;gCAC9B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,KAAK;gCACjE,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4CAAyB,OAAO,SAAS,EAAE;sDACzC,SAAS,IAAI;2CADH,SAAS,EAAE;;;;;;;;;;;;;;;;sCAM9B,6LAAC;sCACC,cAAA,6LAAC;gCACC,OAAO,QAAQ,MAAM,IAAI;gCACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAU;;;;;;kDACxB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAKnG,6LAAC;4BAAM,WAAU;sCACd,KAAK,MAAM,KAAK,kBACf,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS;oCAAG,WAAU;8CAAuC;;;;;;;;;;uCAKnE,KAAK,GAAG,CAAC,CAAC,qBACR,6LAAC;oCAAiB,WAAU;;sDAC1B,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,SAAS,IAAM,SAAS;wDACxB,OAAM;kEAEL,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAI,WAAU;kEACZ,KAAK,OAAO;;;;;;;;;;;;;;;;;sDAInB,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAK,WAAU;0DACb,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;sDAGvB,6LAAC;4CAAG,WAAU;sDACX,eAAe,KAAK,MAAM;;;;;;sDAE7B,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAClB,KAAK,UAAU;;;;;;;;;;;;sDAGpB,6LAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,UAAU,GAAG,oBAAoB;gDAAE,QAAQ,oJAAA,CAAA,OAAI;4CAAC;;;;;;sDAExE,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,OAAO;wDACtB,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,sNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC;wDACC,SAAS,IAAM,SAAS,KAAK,EAAE;wDAC/B,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCA9CpB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DhC;GAjNwB;KAAA", "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/RichTextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEditor, EditorContent } from '@tiptap/react';\nimport StarterKit from '@tiptap/starter-kit';\nimport { useEffect } from 'react';\n\ninterface RichTextEditorProps {\n  content: string;\n  onChange: (content: string) => void;\n  placeholder?: string;\n}\n\nexport default function RichTextEditor({ content, onChange, placeholder }: RichTextEditorProps) {\n  const editor = useEditor({\n    extensions: [StarterKit],\n    content,\n    editorProps: {\n      attributes: {\n        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4',\n      },\n    },\n    onUpdate: ({ editor }) => {\n      onChange(editor.getHTML());\n    },\n  });\n\n  useEffect(() => {\n    if (editor && content !== editor.getHTML()) {\n      editor.commands.setContent(content);\n    }\n  }, [content, editor]);\n\n  if (!editor) {\n    return null;\n  }\n\n  return (\n    <div className=\"border border-gray-300 rounded-lg overflow-hidden\">\n      {/* 工具栏 */}\n      <div className=\"border-b border-gray-300 p-2 bg-gray-50 flex flex-wrap gap-1\">\n        <button\n          onClick={() => editor.chain().focus().toggleBold().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('bold')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          粗体\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleItalic().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('italic')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          斜体\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleStrike().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('strike')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          删除线\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 1 })\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          H1\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 2 })\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          H2\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 3 })\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          H3\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleBulletList().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('bulletList')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          无序列表\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleOrderedList().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('orderedList')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          有序列表\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleBlockquote().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('blockquote')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          引用\n        </button>\n        <button\n          onClick={() => editor.chain().focus().setHorizontalRule().run()}\n          className=\"px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100\"\n        >\n          分割线\n        </button>\n      </div>\n\n      {/* 编辑器内容区域 */}\n      <div className=\"bg-white\">\n        <EditorContent editor={editor} />\n        {!content && placeholder && (\n          <div className=\"absolute top-16 left-4 text-gray-400 pointer-events-none\">\n            {placeholder}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAYe,SAAS,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAuB;;IAC5F,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YAAC,8JAAA,CAAA,UAAU;SAAC;QACxB;QACA,aAAa;YACX,YAAY;gBACV,OAAO;YACT;QACF;QACA,QAAQ;gDAAE,CAAC,EAAE,MAAM,EAAE;gBACnB,SAAS,OAAO,OAAO;YACzB;;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,YAAY,OAAO,OAAO,IAAI;gBAC1C,OAAO,QAAQ,CAAC,UAAU,CAAC;YAC7B;QACF;mCAAG;QAAC;QAAS;KAAO;IAEpB,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACtD,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,UACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,YACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,YACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAClC,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAClC,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAClC,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,gBACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,iBACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,gBACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qKAAA,CAAA,gBAAa;wBAAC,QAAQ;;;;;;oBACtB,CAAC,WAAW,6BACX,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;GA5IwB;;QACP,qKAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/NewsForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport RichTextEditor from './RichTextEditor';\nimport { categoryApi } from '@/lib/api';\nimport { useAuth } from '@/contexts/AuthContext';\nimport type { Category, CreateNewsData, News } from '@/types';\n\nconst newsSchema = z.object({\n  title: z.string().min(1, '标题不能为空').max(200, '标题不能超过200个字符'),\n  excerpt: z.string().min(1, '摘要不能为空').max(500, '摘要不能超过500个字符'),\n  content: z.string().min(1, '内容不能为空'),\n  category_id: z.string().min(1, '请选择分类'),\n  status: z.enum(['draft', 'pending', 'published']),\n  tags: z.string().optional(),\n});\n\ntype NewsFormData = z.infer<typeof newsSchema>;\n\ninterface NewsFormProps {\n  initialData?: News;\n  onSubmit: (data: CreateNewsData) => Promise<void>;\n  onCancel: () => void;\n  isLoading?: boolean;\n}\n\nexport default function NewsForm({ initialData, onSubmit, onCancel, isLoading }: NewsFormProps) {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [content, setContent] = useState(initialData?.content || '');\n  const { isAdmin } = useAuth();\n\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    watch,\n    formState: { errors },\n  } = useForm<NewsFormData>({\n    resolver: zodResolver(newsSchema),\n    defaultValues: {\n      title: initialData?.title || '',\n      excerpt: initialData?.excerpt || '',\n      content: initialData?.content || '',\n      category_id: initialData?.category_id || '',\n      status: initialData?.status || 'draft',\n      tags: initialData?.tags?.join(', ') || '',\n    },\n  });\n\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  useEffect(() => {\n    setValue('content', content);\n  }, [content, setValue]);\n\n  const loadCategories = async () => {\n    try {\n      const data = await categoryApi.getCategories();\n      setCategories(data);\n    } catch (error) {\n      console.error('加载分类失败:', error);\n    }\n  };\n\n  const onFormSubmit = async (data: NewsFormData) => {\n    const tags = data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];\n    \n    await onSubmit({\n      ...data,\n      tags,\n    });\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md\">\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n        {initialData ? '编辑新闻' : '创建新闻'}\n      </h2>\n\n      <form onSubmit={handleSubmit(onFormSubmit)} className=\"space-y-6\">\n        {/* 标题 */}\n        <div>\n          <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            标题 *\n          </label>\n          <input\n            type=\"text\"\n            id=\"title\"\n            {...register('title')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"请输入新闻标题\"\n          />\n          {errors.title && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.title.message}</p>\n          )}\n        </div>\n\n        {/* 摘要 */}\n        <div>\n          <label htmlFor=\"excerpt\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            摘要 *\n          </label>\n          <textarea\n            id=\"excerpt\"\n            rows={3}\n            {...register('excerpt')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"请输入新闻摘要\"\n          />\n          {errors.excerpt && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.excerpt.message}</p>\n          )}\n        </div>\n\n        {/* 分类 */}\n        <div>\n          <label htmlFor=\"category_id\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            分类 *\n          </label>\n          <select\n            id=\"category_id\"\n            {...register('category_id')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">请选择分类</option>\n            {categories.map((category) => (\n              <option key={category.id} value={category.id}>\n                {category.name}\n              </option>\n            ))}\n          </select>\n          {errors.category_id && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.category_id.message}</p>\n          )}\n        </div>\n\n        {/* 标签 */}\n        <div>\n          <label htmlFor=\"tags\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            标签\n          </label>\n          <input\n            type=\"text\"\n            id=\"tags\"\n            {...register('tags')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"请输入标签，用逗号分隔\"\n          />\n          <p className=\"mt-1 text-sm text-gray-500\">多个标签请用逗号分隔</p>\n        </div>\n\n        {/* 内容 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            内容 *\n          </label>\n          <RichTextEditor\n            content={content}\n            onChange={setContent}\n            placeholder=\"请输入新闻内容...\"\n          />\n          {errors.content && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.content.message}</p>\n          )}\n        </div>\n\n        {/* 状态 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            状态\n          </label>\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                value=\"draft\"\n                {...register('status')}\n                className=\"mr-2\"\n              />\n              草稿\n            </label>\n            {isAdmin() && (\n              <label className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  value=\"published\"\n                  {...register('status')}\n                  className=\"mr-2\"\n                />\n                直接发布\n              </label>\n            )}\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                value={isAdmin() ? \"pending\" : \"published\"}\n                {...register('status')}\n                className=\"mr-2\"\n              />\n              {isAdmin() ? \"待审批\" : \"提交发布\"}\n            </label>\n          </div>\n          {!isAdmin() && (\n            <p className=\"mt-1 text-sm text-gray-500\">\n              选择\"提交发布\"将提交给管理员审批\n            </p>\n          )}\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex justify-end space-x-4 pt-6 border-t border-gray-200\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            取消\n          </button>\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isLoading ? '保存中...' : (initialData ? '更新' : '创建')}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAWA,MAAM,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC5C,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC9C,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,QAAQ,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAW;KAAY;IAChD,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC3B;AAWe,SAAS,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAiB;;IAC5F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,WAAW;IAC/D,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE1B,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAgB;QACxB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO,aAAa,SAAS;YAC7B,SAAS,aAAa,WAAW;YACjC,SAAS,aAAa,WAAW;YACjC,aAAa,aAAa,eAAe;YACzC,QAAQ,aAAa,UAAU;YAC/B,MAAM,aAAa,MAAM,KAAK,SAAS;QACzC;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,SAAS,WAAW;QACtB;6BAAG;QAAC;QAAS;KAAS;IAEtB,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,OAAO,MAAM,oHAAA,CAAA,cAAW,CAAC,aAAa;YAC5C,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE;QAEzF,MAAM,SAAS;YACb,GAAG,IAAI;YACP;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BACX,cAAc,SAAS;;;;;;0BAG1B,6LAAC;gBAAK,UAAU,aAAa;gBAAe,WAAU;;kCAEpD,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACF,GAAG,SAAS,QAAQ;gCACrB,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,KAAK,kBACX,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAKlE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAU,WAAU;0CAA+C;;;;;;0CAGlF,6LAAC;gCACC,IAAG;gCACH,MAAM;gCACL,GAAG,SAAS,UAAU;gCACvB,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,OAAO,kBACb,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kCAKpE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAA+C;;;;;;0CAGtF,6LAAC;gCACC,IAAG;gCACF,GAAG,SAAS,cAAc;gCAC3B,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4CAAyB,OAAO,SAAS,EAAE;sDACzC,SAAS,IAAI;2CADH,SAAS,EAAE;;;;;;;;;;;4BAK3B,OAAO,WAAW,kBACjB,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;kCAKxE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA+C;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACF,GAAG,SAAS,OAAO;gCACpB,WAAU;gCACV,aAAY;;;;;;0CAEd,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC,uIAAA,CAAA,UAAc;gCACb,SAAS;gCACT,UAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,OAAO,kBACb,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kCAKpE,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDACC,MAAK;gDACL,OAAM;gDACL,GAAG,SAAS,SAAS;gDACtB,WAAU;;;;;;4CACV;;;;;;;oCAGH,2BACC,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDACC,MAAK;gDACL,OAAM;gDACL,GAAG,SAAS,SAAS;gDACtB,WAAU;;;;;;4CACV;;;;;;;kDAIN,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDACC,MAAK;gDACL,OAAO,YAAY,YAAY;gDAC9B,GAAG,SAAS,SAAS;gDACtB,WAAU;;;;;;4CAEX,YAAY,QAAQ;;;;;;;;;;;;;4BAGxB,CAAC,2BACA,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAO9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,WAAY,cAAc,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAM1D;GA7MwB;;QAGF,kIAAA,CAAA,UAAO;QAQvB,iKAAA,CAAA,UAAO;;;KAXW", "debugId": null}}, {"offset": {"line": 1932, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/NewsViewer.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { format } from 'date-fns';\nimport { zhCN } from 'date-fns/locale';\nimport { \n  XMarkIcon, \n  EyeIcon, \n  CalendarIcon, \n  UserIcon, \n  TagIcon,\n  FolderIcon \n} from '@heroicons/react/24/outline';\nimport { newsApi } from '@/lib/api';\nimport type { NewsWithDetails } from '@/types';\n\ninterface NewsViewerProps {\n  news: NewsWithDetails;\n  onClose: () => void;\n}\n\nexport default function NewsViewer({ news, onClose }: NewsViewerProps) {\n  // 增加浏览量\n  useEffect(() => {\n    const incrementView = async () => {\n      try {\n        await newsApi.incrementViewCount(news.id);\n      } catch (error) {\n        console.error('增加浏览量失败:', error);\n      }\n    };\n\n    // 只对已发布的新闻增加浏览量\n    if (news.status === 'published') {\n      incrementView();\n    }\n  }, [news.id, news.status]);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      draft: { label: '草稿', className: 'bg-gray-100 text-gray-800' },\n      pending: { label: '待审批', className: 'bg-yellow-100 text-yellow-800' },\n      published: { label: '已发布', className: 'bg-green-100 text-green-800' },\n      rejected: { label: '已拒绝', className: 'bg-red-100 text-red-800' },\n      archived: { label: '已归档', className: 'bg-purple-100 text-purple-800' },\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;\n\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}>\n        {config.label}\n      </span>\n    );\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div className=\"relative top-4 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white mb-8\">\n        {/* 头部 */}\n        <div className=\"flex justify-between items-start mb-6\">\n          <div className=\"flex-1 pr-4\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              {news.title}\n            </h1>\n            <div className=\"flex items-center space-x-4 text-sm text-gray-500 mb-4\">\n              {getStatusBadge(news.status)}\n              <div className=\"flex items-center\">\n                <EyeIcon className=\"h-4 w-4 mr-1\" />\n                <span>{news.view_count} 次浏览</span>\n              </div>\n            </div>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 p-1\"\n            title=\"关闭\"\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* 新闻元信息 */}\n        <div className=\"border-b border-gray-200 pb-4 mb-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n            <div className=\"flex items-center text-gray-600\">\n              <UserIcon className=\"h-4 w-4 mr-2\" />\n              <span>作者: {news.author.name}</span>\n            </div>\n            <div className=\"flex items-center text-gray-600\">\n              <FolderIcon className=\"h-4 w-4 mr-2\" />\n              <span>分类: {news.category.name}</span>\n            </div>\n            <div className=\"flex items-center text-gray-600\">\n              <CalendarIcon className=\"h-4 w-4 mr-2\" />\n              <span>\n                创建时间: {format(new Date(news.created_at), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}\n              </span>\n            </div>\n            {news.published_at && (\n              <div className=\"flex items-center text-gray-600\">\n                <CalendarIcon className=\"h-4 w-4 mr-2\" />\n                <span>\n                  发布时间: {format(new Date(news.published_at), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}\n                </span>\n              </div>\n            )}\n          </div>\n\n          {/* 标签 */}\n          {news.tags && news.tags.length > 0 && (\n            <div className=\"mt-4\">\n              <div className=\"flex items-center mb-2\">\n                <TagIcon className=\"h-4 w-4 mr-2 text-gray-600\" />\n                <span className=\"text-sm text-gray-600\">标签:</span>\n              </div>\n              <div className=\"flex flex-wrap gap-2\">\n                {news.tags.map((tag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\"\n                  >\n                    {tag}\n                  </span>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* 摘要 */}\n        <div className=\"mb-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">摘要</h3>\n          <p className=\"text-gray-700 leading-relaxed bg-gray-50 p-4 rounded-lg\">\n            {news.excerpt}\n          </p>\n        </div>\n\n        {/* 新闻内容 */}\n        <div className=\"mb-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">正文</h3>\n          <div \n            className=\"prose prose-sm sm:prose lg:prose-lg xl:prose-xl max-w-none\"\n            dangerouslySetInnerHTML={{ __html: news.content }}\n          />\n        </div>\n\n        {/* 拒绝理由（如果有） */}\n        {news.status === 'rejected' && news.rejection_reason && (\n          <div className=\"mb-6\">\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n              <h4 className=\"text-sm font-medium text-red-800 mb-2\">拒绝理由</h4>\n              <p className=\"text-sm text-red-700\">{news.rejection_reason}</p>\n            </div>\n          </div>\n        )}\n\n        {/* 底部操作栏 */}\n        <div className=\"border-t border-gray-200 pt-4\">\n          <div className=\"flex justify-between items-center\">\n            <div className=\"text-sm text-gray-500\">\n              最后更新: {format(new Date(news.updated_at), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}\n            </div>\n            <button\n              onClick={onClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              关闭\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;AAbA;;;;;;AAqBe,SAAS,WAAW,EAAE,IAAI,EAAE,OAAO,EAAmB;;IACnE,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;sDAAgB;oBACpB,IAAI;wBACF,MAAM,oHAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC,KAAK,EAAE;oBAC1C,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,YAAY;oBAC5B;gBACF;;YAEA,gBAAgB;YAChB,IAAI,KAAK,MAAM,KAAK,aAAa;gBAC/B;YACF;QACF;+BAAG;QAAC,KAAK,EAAE;QAAE,KAAK,MAAM;KAAC;IAEzB,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,OAAO;gBAAE,OAAO;gBAAM,WAAW;YAA4B;YAC7D,SAAS;gBAAE,OAAO;gBAAO,WAAW;YAAgC;YACpE,WAAW;gBAAE,OAAO;gBAAO,WAAW;YAA8B;YACpE,UAAU;gBAAE,OAAO;gBAAO,WAAW;YAA0B;YAC/D,UAAU;gBAAE,OAAO;gBAAO,WAAW;YAAgC;QACvE;QAEA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,KAAK;QAEtF,qBACE,6LAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,OAAO,SAAS,EAAE;sBAC3G,OAAO,KAAK;;;;;;IAGnB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAI,WAAU;;wCACZ,eAAe,KAAK,MAAM;sDAC3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;;wDAAM,KAAK,UAAU;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;sCAI7B,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;;gDAAK;gDAAK,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;;8CAE7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;;gDAAK;gDAAK,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC;;gDAAK;gDACG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,UAAU,GAAG,qBAAqB;oDAAE,QAAQ,oJAAA,CAAA,OAAI;gDAAC;;;;;;;;;;;;;gCAGhF,KAAK,YAAY,kBAChB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDACxB,6LAAC;;gDAAK;gDACG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,YAAY,GAAG,qBAAqB;oDAAE,QAAQ,oJAAA,CAAA,OAAI;gDAAC;;;;;;;;;;;;;;;;;;;wBAOtF,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,6LAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;4CAEC,WAAU;sDAET;2CAHI;;;;;;;;;;;;;;;;;;;;;;8BAYjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCACV,KAAK,OAAO;;;;;;;;;;;;8BAKjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BACC,WAAU;4BACV,yBAAyB;gCAAE,QAAQ,KAAK,OAAO;4BAAC;;;;;;;;;;;;gBAKnD,KAAK,MAAM,KAAK,cAAc,KAAK,gBAAgB,kBAClD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAwB,KAAK,gBAAgB;;;;;;;;;;;;;;;;;8BAMhE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAwB;oCAC9B,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,UAAU,GAAG,qBAAqB;wCAAE,QAAQ,oJAAA,CAAA,OAAI;oCAAC;;;;;;;0CAE/E,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAzJwB;KAAA", "debugId": null}}, {"offset": {"line": 2414, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/UserForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\nimport { userApi } from '@/lib/api';\nimport type { User, CreateUserData, UpdateUserData } from '@/types';\n\nconst createUserSchema = z.object({\n  name: z.string().min(1, '姓名不能为空').max(100, '姓名不能超过100个字符'),\n  email: z.string().email('请输入有效的邮箱地址'),\n  password: z.string().min(6, '密码至少6个字符'),\n  role: z.enum(['admin', 'editor'], { required_error: '请选择角色' }),\n});\n\nconst updateUserSchema = z.object({\n  name: z.string().min(1, '姓名不能为空').max(100, '姓名不能超过100个字符'),\n  email: z.string().email('请输入有效的邮箱地址'),\n  role: z.enum(['admin', 'editor'], { required_error: '请选择角色' }),\n  is_active: z.boolean().optional(),\n});\n\ntype CreateUserFormData = z.infer<typeof createUserSchema>;\ntype UpdateUserFormData = z.infer<typeof updateUserSchema>;\n\ninterface UserFormProps {\n  initialData?: User;\n  onSubmit: () => void;\n  onCancel: () => void;\n}\n\nexport default function UserForm({ initialData, onSubmit, onCancel }: UserFormProps) {\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const isEditing = !!initialData;\n\n  const schema = isEditing ? updateUserSchema : createUserSchema;\n  \n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<CreateUserFormData | UpdateUserFormData>({\n    resolver: zodResolver(schema),\n    defaultValues: isEditing ? {\n      name: initialData.name,\n      email: initialData.email,\n      role: initialData.role,\n      is_active: initialData.is_active !== false,\n    } : {\n      name: '',\n      email: '',\n      password: '',\n      role: 'editor',\n    },\n  });\n\n  const onFormSubmit = async (data: CreateUserFormData | UpdateUserFormData) => {\n    try {\n      setIsLoading(true);\n      \n      if (isEditing) {\n        const updateData = data as UpdateUserFormData;\n        await userApi.updateUser({\n          id: initialData.id,\n          ...updateData,\n        });\n      } else {\n        const createData = data as CreateUserFormData;\n        await userApi.createUser(createData);\n      }\n      \n      onSubmit();\n    } catch (error: any) {\n      console.error('保存用户失败:', error);\n      alert(error.message || '保存失败，请重试');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-md\">\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n        {isEditing ? '编辑用户' : '添加用户'}\n      </h2>\n\n      <form onSubmit={handleSubmit(onFormSubmit)} className=\"space-y-6\">\n        {/* 姓名 */}\n        <div>\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            姓名 *\n          </label>\n          <input\n            type=\"text\"\n            id=\"name\"\n            {...register('name')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"请输入姓名\"\n          />\n          {errors.name && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.name.message}</p>\n          )}\n        </div>\n\n        {/* 邮箱 */}\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            邮箱地址 *\n          </label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            {...register('email')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"请输入邮箱地址\"\n            disabled={isEditing} // 编辑时不允许修改邮箱\n          />\n          {errors.email && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n          )}\n        </div>\n\n        {/* 密码 - 只在创建时显示 */}\n        {!isEditing && (\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              密码 *\n            </label>\n            <div className=\"relative\">\n              <input\n                type={showPassword ? 'text' : 'password'}\n                id=\"password\"\n                {...register('password')}\n                className=\"w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"请输入密码（至少6个字符）\"\n              />\n              <button\n                type=\"button\"\n                className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                onClick={() => setShowPassword(!showPassword)}\n              >\n                {showPassword ? (\n                  <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n                ) : (\n                  <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n                )}\n              </button>\n            </div>\n            {errors.password && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.password.message}</p>\n            )}\n          </div>\n        )}\n\n        {/* 角色 */}\n        <div>\n          <label htmlFor=\"role\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            角色 *\n          </label>\n          <select\n            id=\"role\"\n            {...register('role')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"editor\">编辑</option>\n            <option value=\"admin\">管理员</option>\n          </select>\n          {errors.role && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.role.message}</p>\n          )}\n        </div>\n\n        {/* 状态 - 只在编辑时显示 */}\n        {isEditing && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              账户状态\n            </label>\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"is_active\"\n                {...register('is_active')}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n              <label htmlFor=\"is_active\" className=\"ml-2 block text-sm text-gray-900\">\n                账户激活\n              </label>\n            </div>\n          </div>\n        )}\n\n        {/* 角色说明 */}\n        <div className=\"bg-gray-50 p-4 rounded-md\">\n          <h4 className=\"text-sm font-medium text-gray-900 mb-2\">角色权限说明</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            <li><strong>管理员:</strong> 可以管理所有新闻、用户和分类，审批新闻发布</li>\n            <li><strong>编辑:</strong> 可以创建和编辑自己的新闻，提交发布申请</li>\n          </ul>\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex justify-end space-x-4 pt-6 border-t border-gray-200\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            取消\n          </button>\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isLoading ? '保存中...' : (isEditing ? '更新' : '创建')}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AAUA,MAAM,mBAAmB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC3C,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,MAAM,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAS,EAAE;QAAE,gBAAgB;IAAQ;AAC9D;AAEA,MAAM,mBAAmB,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC3C,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,MAAM,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAS,EAAE;QAAE,gBAAgB;IAAQ;IAC5D,WAAW,oLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;AACjC;AAWe,SAAS,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAiB;;IACjF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,YAAY,CAAC,CAAC;IAEpB,MAAM,SAAS,YAAY,mBAAmB;IAE9C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAA2C;QACnD,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe,YAAY;YACzB,MAAM,YAAY,IAAI;YACtB,OAAO,YAAY,KAAK;YACxB,MAAM,YAAY,IAAI;YACtB,WAAW,YAAY,SAAS,KAAK;QACvC,IAAI;YACF,MAAM;YACN,OAAO;YACP,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,aAAa;YAEb,IAAI,WAAW;gBACb,MAAM,aAAa;gBACnB,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;oBACvB,IAAI,YAAY,EAAE;oBAClB,GAAG,UAAU;gBACf;YACF,OAAO;gBACL,MAAM,aAAa;gBACnB,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YAC3B;YAEA;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM,MAAM,OAAO,IAAI;QACzB,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BACX,YAAY,SAAS;;;;;;0BAGxB,6LAAC;gBAAK,UAAU,aAAa;gBAAe,WAAU;;kCAEpD,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA+C;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACF,GAAG,SAAS,OAAO;gCACpB,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,IAAI,kBACV,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;kCAKjE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACF,GAAG,SAAS,QAAQ;gCACrB,WAAU;gCACV,aAAY;gCACZ,UAAU;;;;;;4BAEX,OAAO,KAAK,kBACX,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;oBAKjE,CAAC,2BACA,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAM,eAAe,SAAS;wCAC9B,IAAG;wCACF,GAAG,SAAS,WAAW;wCACxB,WAAU;wCACV,aAAY;;;;;;kDAEd,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,gBAAgB,CAAC;kDAE/B,6BACC,6LAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;iEAExB,6LAAC,gNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAIxB,OAAO,QAAQ,kBACd,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kCAMvE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA+C;;;;;;0CAG/E,6LAAC;gCACC,IAAG;gCACF,GAAG,SAAS,OAAO;gCACpB,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;4BAEvB,OAAO,IAAI,kBACV,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;oBAKhE,2BACC,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACF,GAAG,SAAS,YAAY;wCACzB,WAAU;;;;;;kDAEZ,6LAAC;wCAAM,SAAQ;wCAAY,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;kCAQ9E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;;0DAAG,6LAAC;0DAAO;;;;;;4CAAa;;;;;;;kDACzB,6LAAC;;0DAAG,6LAAC;0DAAO;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAK5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,WAAY,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAMxD;GA/LwB;;QAWlB,iKAAA,CAAA,UAAO;;;KAXW", "debugId": null}}, {"offset": {"line": 2866, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/UserManagement.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { format } from 'date-fns';\nimport { zhCN } from 'date-fns/locale';\nimport { PlusIcon, PencilIcon, TrashIcon, UserIcon } from '@heroicons/react/24/outline';\nimport { userApi } from '@/lib/api';\nimport { useAuth } from '@/contexts/AuthContext';\nimport UserForm from './UserForm';\nimport type { User } from '@/types';\n\nexport default function UserManagement() {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n  const { user: currentUser, isAdmin } = useAuth();\n\n  useEffect(() => {\n    if (isAdmin()) {\n      loadUsers();\n    }\n  }, [refreshTrigger]);\n\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const data = await userApi.getAllUsers();\n      setUsers(data);\n    } catch (error) {\n      console.error('加载用户失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateUser = () => {\n    setEditingUser(null);\n    setShowForm(true);\n  };\n\n  const handleEditUser = (user: User) => {\n    setEditingUser(user);\n    setShowForm(true);\n  };\n\n  const handleDeleteUser = async (userId: string) => {\n    if (userId === currentUser?.id) {\n      alert('不能删除自己的账户');\n      return;\n    }\n\n    if (!confirm('确定要删除这个用户吗？')) {\n      return;\n    }\n\n    try {\n      await userApi.deleteUser(userId);\n      setRefreshTrigger(prev => prev + 1);\n    } catch (error) {\n      console.error('删除用户失败:', error);\n      alert('删除失败，请重试');\n    }\n  };\n\n  const handleToggleUserStatus = async (userId: string, isActive: boolean) => {\n    if (userId === currentUser?.id) {\n      alert('不能禁用自己的账户');\n      return;\n    }\n\n    try {\n      await userApi.updateUser({\n        id: userId,\n        is_active: !isActive,\n      });\n      setRefreshTrigger(prev => prev + 1);\n    } catch (error) {\n      console.error('更新用户状态失败:', error);\n      alert('操作失败，请重试');\n    }\n  };\n\n  const handleFormSubmit = () => {\n    setShowForm(false);\n    setEditingUser(null);\n    setRefreshTrigger(prev => prev + 1);\n  };\n\n  const handleFormCancel = () => {\n    setShowForm(false);\n    setEditingUser(null);\n  };\n\n  const getRoleBadge = (role: string) => {\n    const roleConfig = {\n      admin: { label: '管理员', className: 'bg-purple-100 text-purple-800' },\n      editor: { label: '编辑', className: 'bg-blue-100 text-blue-800' },\n    };\n\n    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.editor;\n\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}>\n        {config.label}\n      </span>\n    );\n  };\n\n  const getStatusBadge = (isActive: boolean) => {\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n        isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n      }`}>\n        {isActive ? '活跃' : '禁用'}\n      </span>\n    );\n  };\n\n  if (!isAdmin()) {\n    return (\n      <div className=\"text-center py-12\">\n        <UserIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">权限不足</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">只有管理员可以访问用户管理功能</p>\n      </div>\n    );\n  }\n\n  if (showForm) {\n    return (\n      <UserForm\n        initialData={editingUser || undefined}\n        onSubmit={handleFormSubmit}\n        onCancel={handleFormCancel}\n      />\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 头部 */}\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">用户管理</h2>\n        <button\n          onClick={handleCreateUser}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n        >\n          <PlusIcon className=\"h-4 w-4 mr-2\" />\n          添加用户\n        </button>\n      </div>\n\n      {/* 用户列表 */}\n      <div className=\"bg-white shadow-md rounded-lg overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  用户信息\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  角色\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  状态\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  创建时间\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  操作\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {users.length === 0 ? (\n                <tr>\n                  <td colSpan={5} className=\"px-6 py-12 text-center text-gray-500\">\n                    暂无用户数据\n                  </td>\n                </tr>\n              ) : (\n                users.map((user) => (\n                  <tr key={user.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4\">\n                      <div className=\"flex items-center\">\n                        <div className=\"flex-shrink-0 h-10 w-10\">\n                          <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                            <UserIcon className=\"h-6 w-6 text-gray-600\" />\n                          </div>\n                        </div>\n                        <div className=\"ml-4\">\n                          <div className=\"text-sm font-medium text-gray-900\">\n                            {user.name}\n                          </div>\n                          <div className=\"text-sm text-gray-500\">\n                            {user.email}\n                          </div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      {getRoleBadge(user.role)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      {getStatusBadge(user.is_active !== false)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {format(new Date(user.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex space-x-2\">\n                        <button\n                          onClick={() => handleEditUser(user)}\n                          className=\"text-blue-600 hover:text-blue-900\"\n                          title=\"编辑\"\n                        >\n                          <PencilIcon className=\"h-4 w-4\" />\n                        </button>\n                        <button\n                          onClick={() => handleToggleUserStatus(user.id, user.is_active !== false)}\n                          className={`${\n                            user.is_active !== false ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'\n                          }`}\n                          title={user.is_active !== false ? '禁用' : '启用'}\n                          disabled={user.id === currentUser?.id}\n                        >\n                          {user.is_active !== false ? '禁用' : '启用'}\n                        </button>\n                        <button\n                          onClick={() => handleDeleteUser(user.id)}\n                          className=\"text-red-600 hover:text-red-900\"\n                          title=\"删除\"\n                          disabled={user.id === currentUser?.id}\n                        >\n                          <TrashIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))\n              )}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,WAAW;gBACb;YACF;QACF;mCAAG;QAAC;KAAe;IAEnB,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW;YACtC,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,YAAY;IACd;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,WAAW,aAAa,IAAI;YAC9B,MAAM;YACN;QACF;QAEA,IAAI,CAAC,QAAQ,gBAAgB;YAC3B;QACF;QAEA,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YACzB,kBAAkB,CAAA,OAAQ,OAAO;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB,OAAO,QAAgB;QACpD,IAAI,WAAW,aAAa,IAAI;YAC9B,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;gBACvB,IAAI;gBACJ,WAAW,CAAC;YACd;YACA,kBAAkB,CAAA,OAAQ,OAAO;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB;QACvB,YAAY;QACZ,eAAe;QACf,kBAAkB,CAAA,OAAQ,OAAO;IACnC;IAEA,MAAM,mBAAmB;QACvB,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa;YACjB,OAAO;gBAAE,OAAO;gBAAO,WAAW;YAAgC;YAClE,QAAQ;gBAAE,OAAO;gBAAM,WAAW;YAA4B;QAChE;QAEA,MAAM,SAAS,UAAU,CAAC,KAAgC,IAAI,WAAW,MAAM;QAE/E,qBACE,6LAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,OAAO,SAAS,EAAE;sBAC3G,OAAO,KAAK;;;;;;IAGnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,qBACE,6LAAC;YAAK,WAAW,CAAC,wEAAwE,EACxF,WAAW,gCAAgC,2BAC3C;sBACC,WAAW,OAAO;;;;;;IAGzB;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,kNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;8BACpB,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;IAGhD;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,iIAAA,CAAA,UAAQ;YACP,aAAa,eAAe;YAC5B,UAAU;YACV,UAAU;;;;;;IAGhB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,kNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMzC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKnG,6LAAC;gCAAM,WAAU;0CACd,MAAM,MAAM,KAAK,kBAChB,6LAAC;8CACC,cAAA,6LAAC;wCAAG,SAAS;wCAAG,WAAU;kDAAuC;;;;;;;;;;2CAKnE,MAAM,GAAG,CAAC,CAAC,qBACT,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAGxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,KAAK,IAAI;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;8EACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0DAKnB,6LAAC;gDAAG,WAAU;0DACX,aAAa,KAAK,IAAI;;;;;;0DAEzB,6LAAC;gDAAG,WAAU;0DACX,eAAe,KAAK,SAAS,KAAK;;;;;;0DAErC,6LAAC;gDAAG,WAAU;0DACX,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,UAAU,GAAG,oBAAoB;oDAAE,QAAQ,oJAAA,CAAA,OAAI;gDAAC;;;;;;0DAExE,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,SAAS,IAAM,eAAe;4DAC9B,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,sNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;sEAExB,6LAAC;4DACC,SAAS,IAAM,uBAAuB,KAAK,EAAE,EAAE,KAAK,SAAS,KAAK;4DAClE,WAAW,GACT,KAAK,SAAS,KAAK,QAAQ,oCAAoC,uCAC/D;4DACF,OAAO,KAAK,SAAS,KAAK,QAAQ,OAAO;4DACzC,UAAU,KAAK,EAAE,KAAK,aAAa;sEAElC,KAAK,SAAS,KAAK,QAAQ,OAAO;;;;;;sEAErC,6LAAC;4DACC,SAAS,IAAM,iBAAiB,KAAK,EAAE;4DACvC,WAAU;4DACV,OAAM;4DACN,UAAU,KAAK,EAAE,KAAK,aAAa;sEAEnC,cAAA,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCApDpB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiElC;GAxPwB;;QAMiB,kIAAA,CAAA,UAAO;;;KANxB", "debugId": null}}, {"offset": {"line": 3377, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/NewsApproval.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { format } from 'date-fns';\nimport { zhCN } from 'date-fns/locale';\nimport { CheckIcon, XMarkIcon, EyeIcon } from '@heroicons/react/24/outline';\nimport { newsApi } from '@/lib/api';\nimport { useAuth } from '@/contexts/AuthContext';\nimport type { NewsWithDetails } from '@/types';\n\nexport default function NewsApproval() {\n  const [pendingNews, setPendingNews] = useState<NewsWithDetails[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedNews, setSelectedNews] = useState<NewsWithDetails | null>(null);\n  const [rejectionReason, setRejectionReason] = useState('');\n  const [showRejectModal, setShowRejectModal] = useState(false);\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n  const { isAdmin } = useAuth();\n\n  useEffect(() => {\n    if (isAdmin()) {\n      loadPendingNews();\n    }\n  }, [refreshTrigger]);\n\n  const loadPendingNews = async () => {\n    try {\n      setLoading(true);\n      const data = await newsApi.getNews({ status: 'pending' });\n      setPendingNews(data);\n    } catch (error) {\n      console.error('加载待审批新闻失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleApprove = async (newsId: string) => {\n    if (!confirm('确定要批准这条新闻吗？')) {\n      return;\n    }\n\n    try {\n      await newsApi.approveNews(newsId);\n      setRefreshTrigger(prev => prev + 1);\n    } catch (error) {\n      console.error('批准新闻失败:', error);\n      alert('操作失败，请重试');\n    }\n  };\n\n  const handleReject = (news: NewsWithDetails) => {\n    setSelectedNews(news);\n    setRejectionReason('');\n    setShowRejectModal(true);\n  };\n\n  const confirmReject = async () => {\n    if (!selectedNews) return;\n\n    if (!rejectionReason.trim()) {\n      alert('请输入拒绝理由');\n      return;\n    }\n\n    try {\n      await newsApi.rejectNews(selectedNews.id, rejectionReason);\n      setShowRejectModal(false);\n      setSelectedNews(null);\n      setRejectionReason('');\n      setRefreshTrigger(prev => prev + 1);\n    } catch (error) {\n      console.error('拒绝新闻失败:', error);\n      alert('操作失败，请重试');\n    }\n  };\n\n  const handlePreview = (news: NewsWithDetails) => {\n    setSelectedNews(news);\n  };\n\n  if (!isAdmin()) {\n    return (\n      <div className=\"text-center py-12\">\n        <CheckIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n        <h3 className=\"mt-2 text-sm font-medium text-gray-900\">权限不足</h3>\n        <p className=\"mt-1 text-sm text-gray-500\">只有管理员可以审批新闻</p>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* 头部 */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900\">新闻审批</h2>\n        <p className=\"mt-1 text-sm text-gray-500\">\n          共有 {pendingNews.length} 条新闻待审批\n        </p>\n      </div>\n\n      {/* 待审批新闻列表 */}\n      <div className=\"bg-white shadow-md rounded-lg overflow-hidden\">\n        {pendingNews.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <CheckIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">暂无待审批新闻</h3>\n            <p className=\"mt-1 text-sm text-gray-500\">所有新闻都已处理完毕</p>\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {pendingNews.map((news) => (\n              <div key={news.id} className=\"p-6\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                      {news.title}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                      {news.excerpt}\n                    </p>\n                    <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                      <span>作者: {news.author.name}</span>\n                      <span>分类: {news.category.name}</span>\n                      <span>\n                        提交时间: {format(new Date(news.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })}\n                      </span>\n                    </div>\n                    {news.tags && news.tags.length > 0 && (\n                      <div className=\"mt-2 flex flex-wrap gap-1\">\n                        {news.tags.map((tag, index) => (\n                          <span\n                            key={index}\n                            className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800\"\n                          >\n                            {tag}\n                          </span>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"flex items-center space-x-2 ml-4\">\n                    <button\n                      onClick={() => handlePreview(news)}\n                      className=\"inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                    >\n                      <EyeIcon className=\"h-4 w-4 mr-1\" />\n                      预览\n                    </button>\n                    <button\n                      onClick={() => handleApprove(news.id)}\n                      className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n                    >\n                      <CheckIcon className=\"h-4 w-4 mr-1\" />\n                      批准\n                    </button>\n                    <button\n                      onClick={() => handleReject(news)}\n                      className=\"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\n                    >\n                      <XMarkIcon className=\"h-4 w-4 mr-1\" />\n                      拒绝\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* 拒绝理由模态框 */}\n      {showRejectModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n            <div className=\"mt-3\">\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">\n                拒绝新闻发布\n              </h3>\n              <p className=\"text-sm text-gray-600 mb-4\">\n                请输入拒绝理由，这将帮助作者了解需要改进的地方：\n              </p>\n              <textarea\n                value={rejectionReason}\n                onChange={(e) => setRejectionReason(e.target.value)}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"请输入拒绝理由...\"\n              />\n              <div className=\"flex justify-end space-x-3 mt-4\">\n                <button\n                  onClick={() => setShowRejectModal(false)}\n                  className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\"\n                >\n                  取消\n                </button>\n                <button\n                  onClick={confirmReject}\n                  className=\"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700\"\n                >\n                  确认拒绝\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 新闻预览模态框 */}\n      {selectedNews && !showRejectModal && (\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n          <div className=\"relative top-10 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white\">\n            <div className=\"flex justify-between items-start mb-4\">\n              <h3 className=\"text-xl font-bold text-gray-900\">\n                {selectedNews.title}\n              </h3>\n              <button\n                onClick={() => setSelectedNews(null)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <XMarkIcon className=\"h-6 w-6\" />\n              </button>\n            </div>\n            <div className=\"prose max-w-none\">\n              <div className=\"text-sm text-gray-600 mb-4\">\n                {selectedNews.excerpt}\n              </div>\n              <div \n                className=\"content\"\n                dangerouslySetInnerHTML={{ __html: selectedNews.content }}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AAPA;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,WAAW;gBACb;YACF;QACF;iCAAG;QAAC;KAAe;IAEnB,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,oHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAAE,QAAQ;YAAU;YACvD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;QAC9B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,QAAQ,gBAAgB;YAC3B;QACF;QAEA,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;YAC1B,kBAAkB,CAAA,OAAQ,OAAO;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,gBAAgB;QAChB,mBAAmB;QACnB,mBAAmB;IACrB;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,cAAc;QAEnB,IAAI,CAAC,gBAAgB,IAAI,IAAI;YAC3B,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC,aAAa,EAAE,EAAE;YAC1C,mBAAmB;YACnB,gBAAgB;YAChB,mBAAmB;YACnB,kBAAkB,CAAA,OAAQ,OAAO;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;IAClB;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,oNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;8BACrB,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;IAGhD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;;4BAA6B;4BACpC,YAAY,MAAM;4BAAC;;;;;;;;;;;;;0BAK3B,6LAAC;gBAAI,WAAU;0BACZ,YAAY,MAAM,KAAK,kBACtB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;yCAG5C,6LAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;4BAAkB,WAAU;sCAC3B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAE,WAAU;0DACV,KAAK,OAAO;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAK;4DAAK,KAAK,MAAM,CAAC,IAAI;;;;;;;kEAC3B,6LAAC;;4DAAK;4DAAK,KAAK,QAAQ,CAAC,IAAI;;;;;;;kEAC7B,6LAAC;;4DAAK;4DACG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,UAAU,GAAG,oBAAoB;gEAAE,QAAQ,oJAAA,CAAA,OAAI;4DAAC;;;;;;;;;;;;;4CAG/E,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;kDASf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,cAAc;gDAC7B,WAAU;;kEAEV,6LAAC,gNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGtC,6LAAC;gDACC,SAAS,IAAM,cAAc,KAAK,EAAE;gDACpC,WAAU;;kEAEV,6LAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAU;;kEAEV,6LAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;2BAhDpC,KAAK,EAAE;;;;;;;;;;;;;;;YA4DxB,iCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CAGvD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;0CAEd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUV,gBAAgB,CAAC,iCAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,aAAa,KAAK;;;;;;8CAErB,6LAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAGzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,aAAa,OAAO;;;;;;8CAEvB,6LAAC;oCACC,WAAU;oCACV,yBAAyB;wCAAE,QAAQ,aAAa,OAAO;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxE;GA3OwB;;QAOF,kIAAA,CAAA,UAAO;;;KAPL", "debugId": null}}, {"offset": {"line": 3928, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { PlusIcon } from '@heroicons/react/24/outline';\nimport { useAuth } from '@/contexts/AuthContext';\nimport LoginForm from '@/components/LoginForm';\nimport LoginModal from '@/components/LoginModal';\nimport PublicNewsDisplay from '@/components/PublicNewsDisplay';\nimport Navigation from '@/components/Navigation';\nimport NewsList from '@/components/NewsList';\nimport NewsForm from '@/components/NewsForm';\nimport NewsViewer from '@/components/NewsViewer';\nimport UserManagement from '@/components/UserManagement';\nimport NewsApproval from '@/components/NewsApproval';\nimport { newsApi } from '@/lib/api';\nimport type { NewsWithDetails, CreateNewsData } from '@/types';\n\nexport default function Home() {\n  const [currentView, setCurrentView] = useState('news');\n  const [showForm, setShowForm] = useState(false);\n  const [showViewer, setShowViewer] = useState(false);\n  const [showLoginModal, setShowLoginModal] = useState(false);\n  const [editingNews, setEditingNews] = useState<NewsWithDetails | null>(null);\n  const [viewingNews, setViewingNews] = useState<NewsWithDetails | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n  const { user, loading } = useAuth();\n\n  const handleCreateNews = () => {\n    setEditingNews(null);\n    setShowForm(true);\n  };\n\n  const handleEditNews = (news: NewsWithDetails) => {\n    setEditingNews(news);\n    setShowForm(true);\n  };\n\n  const handleViewNews = (news: NewsWithDetails) => {\n    setViewingNews(news);\n    setShowViewer(true);\n  };\n\n  const handleDeleteNews = async (id: string) => {\n    if (!confirm('确定要删除这条新闻吗？')) {\n      return;\n    }\n\n    try {\n      await newsApi.deleteNews(id);\n      setRefreshTrigger(prev => prev + 1);\n    } catch (error) {\n      console.error('删除新闻失败:', error);\n      alert('删除失败，请重试');\n    }\n  };\n\n  const handleSubmitNews = async (data: CreateNewsData) => {\n    try {\n      setIsLoading(true);\n\n      if (editingNews) {\n        await newsApi.updateNews({ ...data, id: editingNews.id });\n      } else {\n        await newsApi.createNews(data);\n      }\n\n      setShowForm(false);\n      setEditingNews(null);\n      setRefreshTrigger(prev => prev + 1);\n    } catch (error) {\n      console.error('保存新闻失败:', error);\n      alert('保存失败，请重试');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setShowForm(false);\n    setEditingNews(null);\n  };\n\n  const handleCloseViewer = () => {\n    setShowViewer(false);\n    setViewingNews(null);\n    setRefreshTrigger(prev => prev + 1); // 刷新列表以更新浏览量\n  };\n\n  const handleViewChange = (view: string) => {\n    setCurrentView(view);\n    setShowForm(false);\n    setShowViewer(false);\n    setEditingNews(null);\n    setViewingNews(null);\n  };\n\n  const handleShowLogin = () => {\n    setShowLoginModal(true);\n  };\n\n  const handleCloseLogin = () => {\n    setShowLoginModal(false);\n  };\n\n  // 显示加载状态\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // 如果用户未登录，显示登录页面\n  if (!user) {\n    return <LoginForm />;\n  }\n\n  // 如果正在显示表单，渲染表单页面\n  if (showForm) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation currentView={currentView} onViewChange={handleViewChange} />\n        <div className=\"py-8\">\n          <NewsForm\n            initialData={editingNews || undefined}\n            onSubmit={handleSubmitNews}\n            onCancel={handleCancel}\n            isLoading={isLoading}\n          />\n        </div>\n      </div>\n    );\n  }\n\n  // 如果正在查看新闻，渲染新闻查看器\n  if (showViewer && viewingNews) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <Navigation currentView={currentView} onViewChange={handleViewChange} />\n        <NewsViewer\n          news={viewingNews}\n          onClose={handleCloseViewer}\n        />\n      </div>\n    );\n  }\n\n  // 渲染主要内容\n  const renderContent = () => {\n    switch (currentView) {\n      case 'news':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"flex justify-between items-center\">\n              <h2 className=\"text-2xl font-bold text-gray-900\">新闻管理</h2>\n              <button\n                onClick={handleCreateNews}\n                className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n              >\n                <PlusIcon className=\"h-4 w-4 mr-2\" />\n                创建新闻\n              </button>\n            </div>\n            <NewsList\n              onEdit={handleEditNews}\n              onDelete={handleDeleteNews}\n              onView={handleViewNews}\n              refreshTrigger={refreshTrigger}\n            />\n          </div>\n        );\n      case 'approval':\n        return <NewsApproval />;\n      case 'users':\n        return <UserManagement />;\n      default:\n        return <div>页面不存在</div>;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navigation currentView={currentView} onViewChange={handleViewChange} />\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {renderContent()}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,mBAAmB;QACvB,eAAe;QACf,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,cAAc;IAChB;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,gBAAgB;YAC3B;QACF;QAEA,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YACzB,kBAAkB,CAAA,OAAQ,OAAO;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,aAAa;YAEb,IAAI,aAAa;gBACf,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;oBAAE,GAAG,IAAI;oBAAE,IAAI,YAAY,EAAE;gBAAC;YACzD,OAAO;gBACL,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YAC3B;YAEA,YAAY;YACZ,eAAe;YACf,kBAAkB,CAAA,OAAQ,OAAO;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,oBAAoB;QACxB,cAAc;QACd,eAAe;QACf,kBAAkB,CAAA,OAAQ,OAAO,IAAI,aAAa;IACpD;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;QACf,YAAY;QACZ,cAAc;QACd,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;IACpB;IAEA,SAAS;IACT,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,iBAAiB;IACjB,IAAI,CAAC,MAAM;QACT,qBAAO,6LAAC,kIAAA,CAAA,UAAS;;;;;IACnB;IAEA,kBAAkB;IAClB,IAAI,UAAU;QACZ,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,UAAU;oBAAC,aAAa;oBAAa,cAAc;;;;;;8BACpD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iIAAA,CAAA,UAAQ;wBACP,aAAa,eAAe;wBAC5B,UAAU;wBACV,UAAU;wBACV,WAAW;;;;;;;;;;;;;;;;;IAKrB;IAEA,mBAAmB;IACnB,IAAI,cAAc,aAAa;QAC7B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,UAAU;oBAAC,aAAa;oBAAa,cAAc;;;;;;8BACpD,6LAAC,mIAAA,CAAA,UAAU;oBACT,MAAM;oBACN,SAAS;;;;;;;;;;;;IAIjB;IAEA,SAAS;IACT,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAIzC,6LAAC,iIAAA,CAAA,UAAQ;4BACP,QAAQ;4BACR,UAAU;4BACV,QAAQ;4BACR,gBAAgB;;;;;;;;;;;;YAIxB,KAAK;gBACH,qBAAO,6LAAC,qIAAA,CAAA,UAAY;;;;;YACtB,KAAK;gBACH,qBAAO,6LAAC,uIAAA,CAAA,UAAc;;;;;YACxB;gBACE,qBAAO,6LAAC;8BAAI;;;;;;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,UAAU;gBAAC,aAAa;gBAAa,cAAc;;;;;;0BACpD,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;GA7KwB;;QASI,kIAAA,CAAA,UAAO;;;KATX", "debugId": null}}]}