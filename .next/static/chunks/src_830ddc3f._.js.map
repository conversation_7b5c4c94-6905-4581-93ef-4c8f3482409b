{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);\n\n// 数据库表结构\nexport const TABLES = {\n  NEWS: 'news',\n  CATEGORIES: 'categories',\n  USERS: 'users',\n} as const;\n"], "names": [], "mappings": ";;;;AAEoB;AAFpB;;AAEA,MAAM,cAAc,8DAAwC;AAC5D,MAAM,kBAAkB,mEAA6C;AAE9D,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,SAAS;IACpB,MAAM;IACN,YAAY;IACZ,OAAO;AACT", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/lib/api.ts"], "sourcesContent": ["import { supabase, TABLES } from './supabase';\nimport type { News, Category, User, CreateNewsData, UpdateNewsData, NewsFilters, NewsWithDetails } from '@/types';\n\n// 新闻相关 API\nexport const newsApi = {\n  // 获取新闻列表\n  async getNews(filters: NewsFilters = {}) {\n    let query = supabase\n      .from(TABLES.NEWS)\n      .select(`\n        *,\n        category:categories(*),\n        author:users(*)\n      `)\n      .order('created_at', { ascending: false });\n\n    if (filters.category_id) {\n      query = query.eq('category_id', filters.category_id);\n    }\n\n    if (filters.status) {\n      query = query.eq('status', filters.status);\n    }\n\n    if (filters.search) {\n      query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`);\n    }\n\n    const limit = filters.limit || 10;\n    const offset = ((filters.page || 1) - 1) * limit;\n    \n    query = query.range(offset, offset + limit - 1);\n\n    const { data, error } = await query;\n    \n    if (error) throw error;\n    return data as NewsWithDetails[];\n  },\n\n  // 获取单个新闻\n  async getNewsById(id: string) {\n    const { data, error } = await supabase\n      .from(TABLES.NEWS)\n      .select(`\n        *,\n        category:categories(*),\n        author:users(*)\n      `)\n      .eq('id', id)\n      .single();\n\n    if (error) throw error;\n    return data as NewsWithDetails;\n  },\n\n  // 创建新闻\n  async createNews(newsData: CreateNewsData) {\n    const { data, error } = await supabase\n      .from(TABLES.NEWS)\n      .insert([{\n        ...newsData,\n        author_id: 'current_user_id', // 这里需要从认证系统获取当前用户ID\n        view_count: 0,\n        published_at: newsData.status === 'published' ? new Date().toISOString() : null,\n      }])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as News;\n  },\n\n  // 更新新闻\n  async updateNews(newsData: UpdateNewsData) {\n    const { id, ...updateData } = newsData;\n    \n    const { data, error } = await supabase\n      .from(TABLES.NEWS)\n      .update({\n        ...updateData,\n        updated_at: new Date().toISOString(),\n        published_at: updateData.status === 'published' ? new Date().toISOString() : undefined,\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as News;\n  },\n\n  // 删除新闻\n  async deleteNews(id: string) {\n    const { error } = await supabase\n      .from(TABLES.NEWS)\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  },\n\n  // 增加浏览量\n  async incrementViewCount(id: string) {\n    const { error } = await supabase.rpc('increment_view_count', { news_id: id });\n    if (error) throw error;\n  },\n};\n\n// 分类相关 API\nexport const categoryApi = {\n  // 获取所有分类\n  async getCategories() {\n    const { data, error } = await supabase\n      .from(TABLES.CATEGORIES)\n      .select('*')\n      .order('name');\n\n    if (error) throw error;\n    return data as Category[];\n  },\n\n  // 创建分类\n  async createCategory(categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>) {\n    const { data, error } = await supabase\n      .from(TABLES.CATEGORIES)\n      .insert([categoryData])\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as Category;\n  },\n\n  // 更新分类\n  async updateCategory(id: string, categoryData: Partial<Category>) {\n    const { data, error } = await supabase\n      .from(TABLES.CATEGORIES)\n      .update({\n        ...categoryData,\n        updated_at: new Date().toISOString(),\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as Category;\n  },\n\n  // 删除分类\n  async deleteCategory(id: string) {\n    const { error } = await supabase\n      .from(TABLES.CATEGORIES)\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  },\n};\n\n// 用户相关 API\nexport const userApi = {\n  // 获取当前用户\n  async getCurrentUser() {\n    const { data: { user } } = await supabase.auth.getUser();\n    return user;\n  },\n\n  // 登录\n  async signIn(email: string, password: string) {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    });\n\n    if (error) throw error;\n    return data;\n  },\n\n  // 登出\n  async signOut() {\n    const { error } = await supabase.auth.signOut();\n    if (error) throw error;\n  },\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AAIO,MAAM,UAAU;IACrB,SAAS;IACT,MAAM,SAAQ,UAAuB,CAAC,CAAC;QACrC,IAAI,QAAQ,yHAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,QAAQ,WAAW,EAAE;YACvB,QAAQ,MAAM,EAAE,CAAC,eAAe,QAAQ,WAAW;QACrD;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,QAAQ,MAAM,EAAE,CAAC,UAAU,QAAQ,MAAM;QAC3C;QAEA,IAAI,QAAQ,MAAM,EAAE;YAClB,QAAQ,MAAM,EAAE,CAAC,CAAC,aAAa,EAAE,QAAQ,MAAM,CAAC,iBAAiB,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;QACtF;QAEA,MAAM,QAAQ,QAAQ,KAAK,IAAI;QAC/B,MAAM,SAAS,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;QAE3C,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAE7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,SAAS;IACT,MAAM,aAAY,EAAU;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,YAAW,QAAwB;QACvC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,CAAC;YAAC;gBACP,GAAG,QAAQ;gBACX,WAAW;gBACX,YAAY;gBACZ,cAAc,SAAS,MAAM,KAAK,cAAc,IAAI,OAAO,WAAW,KAAK;YAC7E;SAAE,EACD,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,YAAW,QAAwB;QACvC,MAAM,EAAE,EAAE,EAAE,GAAG,YAAY,GAAG;QAE9B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,CAAC;YACN,GAAG,UAAU;YACb,YAAY,IAAI,OAAO,WAAW;YAClC,cAAc,WAAW,MAAM,KAAK,cAAc,IAAI,OAAO,WAAW,KAAK;QAC/E,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,YAAW,EAAU;QACzB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,IAAI,EAChB,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,QAAQ;IACR,MAAM,oBAAmB,EAAU;QACjC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,wBAAwB;YAAE,SAAS;QAAG;QAC3E,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM,cAAc;IACzB,SAAS;IACT,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,UAAU,EACtB,MAAM,CAAC,KACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,gBAAe,YAAgE;QACnF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,UAAU,EACtB,MAAM,CAAC;YAAC;SAAa,EACrB,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,gBAAe,EAAU,EAAE,YAA+B;QAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,UAAU,EACtB,MAAM,CAAC;YACN,GAAG,YAAY;YACf,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,gBAAe,EAAU;QAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,yHAAA,CAAA,SAAM,CAAC,UAAU,EACtB,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM,UAAU;IACrB,SAAS;IACT,MAAM;QACJ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QACtD,OAAO;IACT;IAEA,KAAK;IACL,MAAM,QAAO,KAAa,EAAE,QAAgB;QAC1C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QAEA,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,KAAK;IACL,MAAM;QACJ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO,MAAM;IACnB;AACF", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/NewsList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { format } from 'date-fns';\nimport { zhCN } from 'date-fns/locale';\nimport { EyeIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';\nimport { newsApi, categoryApi } from '@/lib/api';\nimport type { NewsWithDetails, Category, NewsFilters } from '@/types';\n\ninterface NewsListProps {\n  onEdit: (news: NewsWithDetails) => void;\n  onDelete: (id: string) => void;\n  refreshTrigger?: number;\n}\n\nexport default function NewsList({ onEdit, onDelete, refreshTrigger }: NewsListProps) {\n  const [news, setNews] = useState<NewsWithDetails[]>([]);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [filters, setFilters] = useState<NewsFilters>({\n    page: 1,\n    limit: 10,\n  });\n\n  useEffect(() => {\n    loadData();\n  }, [filters, refreshTrigger]);\n\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const data = await newsApi.getNews(filters);\n      setNews(data);\n    } catch (error) {\n      console.error('加载新闻失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCategories = async () => {\n    try {\n      const data = await categoryApi.getCategories();\n      setCategories(data);\n    } catch (error) {\n      console.error('加载分类失败:', error);\n    }\n  };\n\n  const handleFilterChange = (key: keyof NewsFilters, value: string) => {\n    setFilters(prev => ({\n      ...prev,\n      [key]: value || undefined,\n      page: 1, // 重置页码\n    }));\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      draft: { label: '草稿', className: 'bg-gray-100 text-gray-800' },\n      published: { label: '已发布', className: 'bg-green-100 text-green-800' },\n      archived: { label: '已归档', className: 'bg-red-100 text-red-800' },\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;\n\n    return (\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}>\n        {config.label}\n      </span>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white shadow-md rounded-lg overflow-hidden\">\n      {/* 筛选器 */}\n      <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n        <div className=\"flex flex-wrap gap-4\">\n          <div className=\"flex-1 min-w-64\">\n            <input\n              type=\"text\"\n              placeholder=\"搜索新闻标题或内容...\"\n              value={filters.search || ''}\n              onChange={(e) => handleFilterChange('search', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <div>\n            <select\n              value={filters.category_id || ''}\n              onChange={(e) => handleFilterChange('category_id', e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">所有分类</option>\n              {categories.map((category) => (\n                <option key={category.id} value={category.id}>\n                  {category.name}\n                </option>\n              ))}\n            </select>\n          </div>\n          <div>\n            <select\n              value={filters.status || ''}\n              onChange={(e) => handleFilterChange('status', e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">所有状态</option>\n              <option value=\"draft\">草稿</option>\n              <option value=\"published\">已发布</option>\n              <option value=\"archived\">已归档</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* 新闻列表 */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"min-w-full divide-y divide-gray-200\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                标题\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                分类\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                状态\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                浏览量\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                创建时间\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                操作\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {news.length === 0 ? (\n              <tr>\n                <td colSpan={6} className=\"px-6 py-12 text-center text-gray-500\">\n                  暂无新闻数据\n                </td>\n              </tr>\n            ) : (\n              news.map((item) => (\n                <tr key={item.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4\">\n                    <div className=\"max-w-xs\">\n                      <div className=\"text-sm font-medium text-gray-900 truncate\">\n                        {item.title}\n                      </div>\n                      <div className=\"text-sm text-gray-500 truncate\">\n                        {item.excerpt}\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                      {item.category.name}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {getStatusBadge(item.status)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center text-sm text-gray-500\">\n                      <EyeIcon className=\"h-4 w-4 mr-1\" />\n                      {item.view_count}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    {format(new Date(item.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => onEdit(item)}\n                        className=\"text-blue-600 hover:text-blue-900\"\n                        title=\"编辑\"\n                      >\n                        <PencilIcon className=\"h-4 w-4\" />\n                      </button>\n                      <button\n                        onClick={() => onDelete(item.id)}\n                        className=\"text-red-600 hover:text-red-900\"\n                        title=\"删除\"\n                      >\n                        <TrashIcon className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAee,SAAS,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAiB;;IAClF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,MAAM;QACN,OAAO;IACT;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG;QAAC;QAAS;KAAe;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,MAAM,WAAW;QACf,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,oHAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YACnC,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,OAAO,MAAM,oHAAA,CAAA,cAAW,CAAC,aAAa;YAC5C,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,qBAAqB,CAAC,KAAwB;QAClD,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE,SAAS;gBAChB,MAAM;YACR,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,OAAO;gBAAE,OAAO;gBAAM,WAAW;YAA4B;YAC7D,WAAW;gBAAE,OAAO;gBAAO,WAAW;YAA8B;YACpE,UAAU;gBAAE,OAAO;gBAAO,WAAW;YAA0B;QACjE;QAEA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,KAAK;QAEtF,qBACE,6LAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,OAAO,SAAS,EAAE;sBAC3G,OAAO,KAAK;;;;;;IAGnB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,QAAQ,MAAM,IAAI;gCACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;;;;;;;;;;;sCAGd,6LAAC;sCACC,cAAA,6LAAC;gCACC,OAAO,QAAQ,WAAW,IAAI;gCAC9B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,KAAK;gCACjE,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4CAAyB,OAAO,SAAS,EAAE;sDACzC,SAAS,IAAI;2CADH,SAAS,EAAE;;;;;;;;;;;;;;;;sCAM9B,6LAAC;sCACC,cAAA,6LAAC;gCACC,OAAO,QAAQ,MAAM,IAAI;gCACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,6LAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;4BAAM,WAAU;sCACf,cAAA,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;kDAG/F,6LAAC;wCAAG,WAAU;kDAAiF;;;;;;;;;;;;;;;;;sCAKnG,6LAAC;4BAAM,WAAU;sCACd,KAAK,MAAM,KAAK,kBACf,6LAAC;0CACC,cAAA,6LAAC;oCAAG,SAAS;oCAAG,WAAU;8CAAuC;;;;;;;;;;uCAKnE,KAAK,GAAG,CAAC,CAAC,qBACR,6LAAC;oCAAiB,WAAU;;sDAC1B,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAI,WAAU;kEACZ,KAAK,OAAO;;;;;;;;;;;;;;;;;sDAInB,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAK,WAAU;0DACb,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;sDAGvB,6LAAC;4CAAG,WAAU;sDACX,eAAe,KAAK,MAAM;;;;;;sDAE7B,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAClB,KAAK,UAAU;;;;;;;;;;;;sDAGpB,6LAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,UAAU,GAAG,oBAAoB;gDAAE,QAAQ,oJAAA,CAAA,OAAI;4CAAC;;;;;;sDAExE,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,OAAO;wDACtB,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,sNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC;wDACC,SAAS,IAAM,SAAS,KAAK,EAAE;wDAC/B,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCA1CpB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDhC;GAzMwB;KAAA", "debugId": null}}, {"offset": {"line": 664, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/RichTextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEditor, EditorContent } from '@tiptap/react';\nimport StarterKit from '@tiptap/starter-kit';\nimport { useEffect } from 'react';\n\ninterface RichTextEditorProps {\n  content: string;\n  onChange: (content: string) => void;\n  placeholder?: string;\n}\n\nexport default function RichTextEditor({ content, onChange, placeholder }: RichTextEditorProps) {\n  const editor = useEditor({\n    extensions: [StarterKit],\n    content,\n    editorProps: {\n      attributes: {\n        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4',\n      },\n    },\n    onUpdate: ({ editor }) => {\n      onChange(editor.getHTML());\n    },\n  });\n\n  useEffect(() => {\n    if (editor && content !== editor.getHTML()) {\n      editor.commands.setContent(content);\n    }\n  }, [content, editor]);\n\n  if (!editor) {\n    return null;\n  }\n\n  return (\n    <div className=\"border border-gray-300 rounded-lg overflow-hidden\">\n      {/* 工具栏 */}\n      <div className=\"border-b border-gray-300 p-2 bg-gray-50 flex flex-wrap gap-1\">\n        <button\n          onClick={() => editor.chain().focus().toggleBold().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('bold')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          粗体\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleItalic().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('italic')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          斜体\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleStrike().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('strike')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          删除线\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 1 })\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          H1\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 2 })\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          H2\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('heading', { level: 3 })\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          H3\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleBulletList().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('bulletList')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          无序列表\n        </button>\n        <button\n          onClick={() => editor.chain().focus().toggleOrderedList().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('orderedList')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          有序列表\n        </button>\n        <div className=\"w-px h-6 bg-gray-300 mx-1\"></div>\n        <button\n          onClick={() => editor.chain().focus().toggleBlockquote().run()}\n          className={`px-3 py-1 rounded text-sm font-medium ${\n            editor.isActive('blockquote')\n              ? 'bg-blue-500 text-white'\n              : 'bg-white text-gray-700 hover:bg-gray-100'\n          }`}\n        >\n          引用\n        </button>\n        <button\n          onClick={() => editor.chain().focus().setHorizontalRule().run()}\n          className=\"px-3 py-1 rounded text-sm font-medium bg-white text-gray-700 hover:bg-gray-100\"\n        >\n          分割线\n        </button>\n      </div>\n\n      {/* 编辑器内容区域 */}\n      <div className=\"bg-white\">\n        <EditorContent editor={editor} />\n        {!content && placeholder && (\n          <div className=\"absolute top-16 left-4 text-gray-400 pointer-events-none\">\n            {placeholder}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAYe,SAAS,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAuB;;IAC5F,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE;QACvB,YAAY;YAAC,8JAAA,CAAA,UAAU;SAAC;QACxB;QACA,aAAa;YACX,YAAY;gBACV,OAAO;YACT;QACF;QACA,QAAQ;gDAAE,CAAC,EAAE,MAAM,EAAE;gBACnB,SAAS,OAAO,OAAO;YACzB;;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,YAAY,OAAO,OAAO,IAAI;gBAC1C,OAAO,QAAQ,CAAC,UAAU,CAAC;YAC7B;QACF;mCAAG;QAAC;QAAS;KAAO;IAEpB,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;wBACtD,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,UACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,YACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;wBACxD,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,YACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAClC,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAClC,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,aAAa,CAAC;gCAAE,OAAO;4BAAE,GAAG,GAAG;wBACrE,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,WAAW;4BAAE,OAAO;wBAAE,KAClC,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,gBACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,iBACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,gBAAgB,GAAG,GAAG;wBAC5D,WAAW,CAAC,sCAAsC,EAChD,OAAO,QAAQ,CAAC,gBACZ,2BACA,4CACJ;kCACH;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,OAAO,KAAK,GAAG,KAAK,GAAG,iBAAiB,GAAG,GAAG;wBAC7D,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qKAAA,CAAA,gBAAa;wBAAC,QAAQ;;;;;;oBACtB,CAAC,WAAW,6BACX,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAMb;GA5IwB;;QACP,qKAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/components/NewsForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport RichTextEditor from './RichTextEditor';\nimport { categoryApi } from '@/lib/api';\nimport type { Category, CreateNewsData, News } from '@/types';\n\nconst newsSchema = z.object({\n  title: z.string().min(1, '标题不能为空').max(200, '标题不能超过200个字符'),\n  excerpt: z.string().min(1, '摘要不能为空').max(500, '摘要不能超过500个字符'),\n  content: z.string().min(1, '内容不能为空'),\n  category_id: z.string().min(1, '请选择分类'),\n  status: z.enum(['draft', 'published']),\n  tags: z.string().optional(),\n});\n\ntype NewsFormData = z.infer<typeof newsSchema>;\n\ninterface NewsFormProps {\n  initialData?: News;\n  onSubmit: (data: CreateNewsData) => Promise<void>;\n  onCancel: () => void;\n  isLoading?: boolean;\n}\n\nexport default function NewsForm({ initialData, onSubmit, onCancel, isLoading }: NewsFormProps) {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [content, setContent] = useState(initialData?.content || '');\n\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    watch,\n    formState: { errors },\n  } = useForm<NewsFormData>({\n    resolver: zodResolver(newsSchema),\n    defaultValues: {\n      title: initialData?.title || '',\n      excerpt: initialData?.excerpt || '',\n      content: initialData?.content || '',\n      category_id: initialData?.category_id || '',\n      status: initialData?.status || 'draft',\n      tags: initialData?.tags?.join(', ') || '',\n    },\n  });\n\n  useEffect(() => {\n    loadCategories();\n  }, []);\n\n  useEffect(() => {\n    setValue('content', content);\n  }, [content, setValue]);\n\n  const loadCategories = async () => {\n    try {\n      const data = await categoryApi.getCategories();\n      setCategories(data);\n    } catch (error) {\n      console.error('加载分类失败:', error);\n    }\n  };\n\n  const onFormSubmit = async (data: NewsFormData) => {\n    const tags = data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];\n    \n    await onSubmit({\n      ...data,\n      tags,\n    });\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md\">\n      <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n        {initialData ? '编辑新闻' : '创建新闻'}\n      </h2>\n\n      <form onSubmit={handleSubmit(onFormSubmit)} className=\"space-y-6\">\n        {/* 标题 */}\n        <div>\n          <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            标题 *\n          </label>\n          <input\n            type=\"text\"\n            id=\"title\"\n            {...register('title')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"请输入新闻标题\"\n          />\n          {errors.title && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.title.message}</p>\n          )}\n        </div>\n\n        {/* 摘要 */}\n        <div>\n          <label htmlFor=\"excerpt\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            摘要 *\n          </label>\n          <textarea\n            id=\"excerpt\"\n            rows={3}\n            {...register('excerpt')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"请输入新闻摘要\"\n          />\n          {errors.excerpt && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.excerpt.message}</p>\n          )}\n        </div>\n\n        {/* 分类 */}\n        <div>\n          <label htmlFor=\"category_id\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            分类 *\n          </label>\n          <select\n            id=\"category_id\"\n            {...register('category_id')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"\">请选择分类</option>\n            {categories.map((category) => (\n              <option key={category.id} value={category.id}>\n                {category.name}\n              </option>\n            ))}\n          </select>\n          {errors.category_id && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.category_id.message}</p>\n          )}\n        </div>\n\n        {/* 标签 */}\n        <div>\n          <label htmlFor=\"tags\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n            标签\n          </label>\n          <input\n            type=\"text\"\n            id=\"tags\"\n            {...register('tags')}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            placeholder=\"请输入标签，用逗号分隔\"\n          />\n          <p className=\"mt-1 text-sm text-gray-500\">多个标签请用逗号分隔</p>\n        </div>\n\n        {/* 内容 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            内容 *\n          </label>\n          <RichTextEditor\n            content={content}\n            onChange={setContent}\n            placeholder=\"请输入新闻内容...\"\n          />\n          {errors.content && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.content.message}</p>\n          )}\n        </div>\n\n        {/* 状态 */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            状态\n          </label>\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                value=\"draft\"\n                {...register('status')}\n                className=\"mr-2\"\n              />\n              草稿\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                value=\"published\"\n                {...register('status')}\n                className=\"mr-2\"\n              />\n              发布\n            </label>\n          </div>\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex justify-end space-x-4 pt-6 border-t border-gray-200\">\n          <button\n            type=\"button\"\n            onClick={onCancel}\n            className=\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            取消\n          </button>\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isLoading ? '保存中...' : (initialData ? '更新' : '创建')}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AAUA,MAAM,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC5C,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC9C,SAAS,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,QAAQ,oLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAS;KAAY;IACrC,MAAM,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC3B;AAWe,SAAS,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAiB;;IAC5F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,WAAW;IAE/D,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAgB;QACxB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO,aAAa,SAAS;YAC7B,SAAS,aAAa,WAAW;YACjC,SAAS,aAAa,WAAW;YACjC,aAAa,aAAa,eAAe;YACzC,QAAQ,aAAa,UAAU;YAC/B,MAAM,aAAa,MAAM,KAAK,SAAS;QACzC;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,SAAS,WAAW;QACtB;6BAAG;QAAC;QAAS;KAAS;IAEtB,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,OAAO,MAAM,oHAAA,CAAA,cAAW,CAAC,aAAa;YAC5C,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,OAAO,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE;QAEzF,MAAM,SAAS;YACb,GAAG,IAAI;YACP;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BACX,cAAc,SAAS;;;;;;0BAG1B,6LAAC;gBAAK,UAAU,aAAa;gBAAe,WAAU;;kCAEpD,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACF,GAAG,SAAS,QAAQ;gCACrB,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,KAAK,kBACX,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kCAKlE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAU,WAAU;0CAA+C;;;;;;0CAGlF,6LAAC;gCACC,IAAG;gCACH,MAAM;gCACL,GAAG,SAAS,UAAU;gCACvB,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,OAAO,kBACb,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kCAKpE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAA+C;;;;;;0CAGtF,6LAAC;gCACC,IAAG;gCACF,GAAG,SAAS,cAAc;gCAC3B,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4CAAyB,OAAO,SAAS,EAAE;sDACzC,SAAS,IAAI;2CADH,SAAS,EAAE;;;;;;;;;;;4BAK3B,OAAO,WAAW,kBACjB,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;kCAKxE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA+C;;;;;;0CAG/E,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACF,GAAG,SAAS,OAAO;gCACpB,WAAU;gCACV,aAAY;;;;;;0CAEd,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC,uIAAA,CAAA,UAAc;gCACb,SAAS;gCACT,UAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,OAAO,kBACb,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;kCAKpE,6LAAC;;0CACC,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDACC,MAAK;gDACL,OAAM;gDACL,GAAG,SAAS,SAAS;gDACtB,WAAU;;;;;;4CACV;;;;;;;kDAGJ,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;gDACC,MAAK;gDACL,OAAM;gDACL,GAAG,SAAS,SAAS;gDACtB,WAAU;;;;;;4CACV;;;;;;;;;;;;;;;;;;;kCAOR,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,WAAY,cAAc,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAM1D;GA5LwB;;QAUlB,iKAAA,CAAA,UAAO;;;KAVW", "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Documents/augment-projects/newssystem/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { PlusIcon } from '@heroicons/react/24/outline';\nimport NewsList from '@/components/NewsList';\nimport NewsForm from '@/components/NewsForm';\nimport { newsApi } from '@/lib/api';\nimport type { NewsWithDetails, CreateNewsData } from '@/types';\n\nexport default function Home() {\n  const [showForm, setShowForm] = useState(false);\n  const [editingNews, setEditingNews] = useState<NewsWithDetails | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n\n  const handleCreateNews = () => {\n    setEditingNews(null);\n    setShowForm(true);\n  };\n\n  const handleEditNews = (news: NewsWithDetails) => {\n    setEditingNews(news);\n    setShowForm(true);\n  };\n\n  const handleDeleteNews = async (id: string) => {\n    if (!confirm('确定要删除这条新闻吗？')) {\n      return;\n    }\n\n    try {\n      await newsApi.deleteNews(id);\n      setRefreshTrigger(prev => prev + 1);\n    } catch (error) {\n      console.error('删除新闻失败:', error);\n      alert('删除失败，请重试');\n    }\n  };\n\n  const handleSubmitNews = async (data: CreateNewsData) => {\n    try {\n      setIsLoading(true);\n\n      if (editingNews) {\n        await newsApi.updateNews({ ...data, id: editingNews.id });\n      } else {\n        await newsApi.createNews(data);\n      }\n\n      setShowForm(false);\n      setEditingNews(null);\n      setRefreshTrigger(prev => prev + 1);\n    } catch (error) {\n      console.error('保存新闻失败:', error);\n      alert('保存失败，请重试');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    setShowForm(false);\n    setEditingNews(null);\n  };\n\n  if (showForm) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-8\">\n        <NewsForm\n          initialData={editingNews || undefined}\n          onSubmit={handleSubmitNews}\n          onCancel={handleCancel}\n          isLoading={isLoading}\n        />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 头部 */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              公司新闻发布系统\n            </h1>\n            <button\n              onClick={handleCreateNews}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <PlusIcon className=\"h-4 w-4 mr-2\" />\n              创建新闻\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* 主要内容 */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <NewsList\n          onEdit={handleEditNews}\n          onDelete={handleDeleteNews}\n          refreshTrigger={refreshTrigger}\n        />\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,mBAAmB;QACvB,eAAe;QACf,YAAY;IACd;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,YAAY;IACd;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,QAAQ,gBAAgB;YAC3B;QACF;QAEA,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YACzB,kBAAkB,CAAA,OAAQ,OAAO;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,aAAa;YAEb,IAAI,aAAa;gBACf,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;oBAAE,GAAG,IAAI;oBAAE,IAAI,YAAY,EAAE;gBAAC;YACzD,OAAO;gBACL,MAAM,oHAAA,CAAA,UAAO,CAAC,UAAU,CAAC;YAC3B;YAEA,YAAY;YACZ,eAAe;YACf,kBAAkB,CAAA,OAAQ,OAAO;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ,eAAe;IACjB;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,iIAAA,CAAA,UAAQ;gBACP,aAAa,eAAe;gBAC5B,UAAU;gBACV,UAAU;gBACV,WAAW;;;;;;;;;;;IAInB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,iIAAA,CAAA,UAAQ;oBACP,QAAQ;oBACR,UAAU;oBACV,gBAAgB;;;;;;;;;;;;;;;;;AAK1B;GAnGwB;KAAA", "debugId": null}}]}