(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "TABLES": (()=>TABLES),
    "supabase": (()=>supabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-client] (ecmascript) <locals>");
;
const supabaseUrl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo.supabase.co';
const supabaseAnonKey = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-key';
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
const TABLES = {
    NEWS: 'news',
    CATEGORIES: 'categories',
    USERS: 'users'
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/mockData.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "mockCategoryApi": (()=>mockCategoryApi),
    "mockNewsApi": (()=>mockNewsApi),
    "mockUserApi": (()=>mockUserApi)
});
// 模拟数据
const mockCategories = [
    {
        id: '1',
        name: '公司新闻',
        description: '公司内部新闻和公告',
        slug: 'company-news',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: '2',
        name: '行业动态',
        description: '行业相关新闻和趋势',
        slug: 'industry-news',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    },
    {
        id: '3',
        name: '产品发布',
        description: '新产品发布和更新',
        slug: 'product-releases',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
    }
];
let mockUsers = [
    {
        id: 'user1',
        email: '<EMAIL>',
        name: '系统管理员',
        role: 'admin',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        is_active: true
    },
    {
        id: 'user2',
        email: '<EMAIL>',
        name: '编辑',
        role: 'editor',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        is_active: true
    },
    {
        id: 'user3',
        email: '<EMAIL>',
        name: '编辑小王',
        role: 'editor',
        created_at: '2024-01-02T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
        is_active: true
    }
];
// 模拟密码存储（实际应用中应该使用加密）
const mockPasswords = {
    '<EMAIL>': 'admin123',
    '<EMAIL>': 'editor123',
    '<EMAIL>': 'editor123'
};
let mockNews = [
    {
        id: 'news1',
        title: '公司2024年度总结大会成功举办',
        content: '<h2>会议概况</h2><p>2024年12月15日，我公司在总部大楼成功举办了年度总结大会。本次大会回顾了过去一年的重要成就，并对未来发展进行了规划。</p><h3>主要议题</h3><ul><li>2024年业绩回顾</li><li>优秀员工表彰</li><li>2025年发展规划</li></ul><p>会议取得了圆满成功，为公司未来发展奠定了坚实基础。</p>',
        excerpt: '公司年度总结大会成功举办，回顾成就，规划未来发展方向。',
        category_id: '1',
        author_id: 'user1',
        status: 'published',
        published_at: '2024-12-15T10:00:00Z',
        created_at: '2024-12-15T09:00:00Z',
        updated_at: '2024-12-15T09:00:00Z',
        view_count: 156,
        tags: [
            '年度总结',
            '会议',
            '规划'
        ],
        category: mockCategories[0],
        author: mockUsers[0]
    },
    {
        id: 'news2',
        title: '新产品发布：智能办公系统V2.0',
        content: '<h2>产品介绍</h2><p>我们很高兴地宣布，智能办公系统V2.0正式发布！新版本带来了许多令人兴奋的功能和改进。</p><h3>主要新功能</h3><ul><li>AI智能助手</li><li>实时协作编辑</li><li>移动端优化</li><li>数据可视化仪表板</li></ul><p>欢迎所有用户升级体验！</p>',
        excerpt: '智能办公系统V2.0正式发布，带来AI助手、实时协作等新功能。',
        category_id: '3',
        author_id: 'user2',
        status: 'published',
        published_at: '2024-12-10T14:00:00Z',
        created_at: '2024-12-10T13:00:00Z',
        updated_at: '2024-12-10T13:00:00Z',
        view_count: 89,
        tags: [
            '产品发布',
            '智能办公',
            'AI'
        ],
        category: mockCategories[2],
        author: mockUsers[1]
    },
    {
        id: 'news3',
        title: '行业报告：2024年技术发展趋势分析',
        content: '<h2>报告摘要</h2><p>根据最新的行业调研数据，2024年技术发展呈现出以下几个重要趋势...</p><h3>主要趋势</h3><ol><li>人工智能技术的普及应用</li><li>云计算服务的深度整合</li><li>数据安全重要性日益凸显</li></ol><p>这些趋势将深刻影响未来几年的技术发展方向。</p>',
        excerpt: '深度分析2024年技术发展趋势，包括AI、云计算、数据安全等重点领域。',
        category_id: '2',
        author_id: 'user1',
        status: 'draft',
        created_at: '2024-12-08T16:00:00Z',
        updated_at: '2024-12-08T16:00:00Z',
        view_count: 23,
        tags: [
            '行业报告',
            '技术趋势',
            '分析'
        ],
        category: mockCategories[1],
        author: mockUsers[0]
    },
    {
        id: 'news4',
        title: '新员工培训计划启动通知',
        content: '<h2>培训计划概述</h2><p>为了帮助新员工更好地融入公司文化，我们将启动全新的员工培训计划。</p><h3>培训内容</h3><ul><li>公司文化和价值观</li><li>业务流程介绍</li><li>技能培训</li><li>团队建设活动</li></ul><p>请各部门积极配合，确保培训效果。</p>',
        excerpt: '公司启动新员工培训计划，包含文化介绍、业务流程、技能培训等内容。',
        category_id: '1',
        author_id: 'user2',
        status: 'pending',
        created_at: '2024-12-09T10:00:00Z',
        updated_at: '2024-12-09T10:00:00Z',
        view_count: 0,
        tags: [
            '培训',
            '新员工',
            '通知'
        ],
        category: mockCategories[0],
        author: mockUsers[1]
    },
    {
        id: 'news5',
        title: '技术分享：React 19 新特性解析',
        content: '<h2>React 19 重要更新</h2><p>React 19 带来了许多令人兴奋的新特性和改进...</p><h3>主要特性</h3><ul><li>并发渲染优化</li><li>新的 Hooks API</li><li>服务器组件增强</li><li>性能提升</li></ul><p>让我们一起探索这些新特性如何改善开发体验。</p>',
        excerpt: 'React 19 新特性详细解析，包括并发渲染、新 Hooks、服务器组件等。',
        category_id: '3',
        author_id: 'user3',
        status: 'pending',
        created_at: '2024-12-09T14:00:00Z',
        updated_at: '2024-12-09T14:00:00Z',
        view_count: 0,
        tags: [
            'React',
            '技术分享',
            '前端'
        ],
        category: mockCategories[2],
        author: mockUsers[2]
    }
];
// 模拟 API 延迟
const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));
const mockNewsApi = {
    async getNews (filters = {}) {
        await delay(500); // 模拟网络延迟
        let filteredNews = [
            ...mockNews
        ];
        if (filters.category_id) {
            filteredNews = filteredNews.filter((news)=>news.category_id === filters.category_id);
        }
        if (filters.status) {
            filteredNews = filteredNews.filter((news)=>news.status === filters.status);
        }
        if (filters.search) {
            const searchLower = filters.search.toLowerCase();
            filteredNews = filteredNews.filter((news)=>news.title.toLowerCase().includes(searchLower) || news.content.toLowerCase().includes(searchLower));
        }
        // 简单分页
        const limit = filters.limit || 10;
        const offset = ((filters.page || 1) - 1) * limit;
        return filteredNews.slice(offset, offset + limit);
    },
    async getNewsById (id) {
        await delay(300);
        const news = mockNews.find((n)=>n.id === id);
        if (!news) throw new Error('新闻不存在');
        return news;
    },
    async createNews (newsData) {
        await delay(800);
        // 根据用户角色决定新闻状态
        let finalStatus = newsData.status;
        if (currentUser?.role === 'editor' && newsData.status === 'published') {
            finalStatus = 'pending'; // 编辑提交发布申请，状态改为待审批
        }
        const newNews = {
            id: `news${Date.now()}`,
            ...newsData,
            status: finalStatus,
            author_id: currentUser?.id || 'user1',
            view_count: 0,
            published_at: finalStatus === 'published' ? new Date().toISOString() : undefined,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            category: mockCategories.find((c)=>c.id === newsData.category_id) || mockCategories[0],
            author: currentUser || mockUsers[0]
        };
        mockNews.unshift(newNews);
        return newNews;
    },
    async updateNews (newsData) {
        await delay(800);
        const index = mockNews.findIndex((n)=>n.id === newsData.id);
        if (index === -1) throw new Error('新闻不存在');
        const { id, ...updateData } = newsData;
        mockNews[index] = {
            ...mockNews[index],
            ...updateData,
            updated_at: new Date().toISOString(),
            published_at: updateData.status === 'published' ? new Date().toISOString() : mockNews[index].published_at,
            category: updateData.category_id ? mockCategories.find((c)=>c.id === updateData.category_id) || mockNews[index].category : mockNews[index].category
        };
        return mockNews[index];
    },
    async deleteNews (id) {
        await delay(500);
        const index = mockNews.findIndex((n)=>n.id === id);
        if (index === -1) throw new Error('新闻不存在');
        mockNews.splice(index, 1);
    },
    async incrementViewCount (id) {
        await delay(200);
        const news = mockNews.find((n)=>n.id === id);
        if (news) {
            news.view_count += 1;
        }
    },
    async approveNews (newsId) {
        await delay(500);
        const index = mockNews.findIndex((n)=>n.id === newsId);
        if (index === -1) throw new Error('新闻不存在');
        mockNews[index] = {
            ...mockNews[index],
            status: 'published',
            published_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        return mockNews[index];
    },
    async rejectNews (newsId, reason) {
        await delay(500);
        const index = mockNews.findIndex((n)=>n.id === newsId);
        if (index === -1) throw new Error('新闻不存在');
        mockNews[index] = {
            ...mockNews[index],
            status: 'rejected',
            rejection_reason: reason,
            updated_at: new Date().toISOString()
        };
        return mockNews[index];
    }
};
const mockCategoryApi = {
    async getCategories () {
        await delay(300);
        return [
            ...mockCategories
        ];
    },
    async createCategory (categoryData) {
        await delay(500);
        const newCategory = {
            id: `cat${Date.now()}`,
            ...categoryData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        mockCategories.push(newCategory);
        return newCategory;
    },
    async updateCategory (id, categoryData) {
        await delay(500);
        const index = mockCategories.findIndex((c)=>c.id === id);
        if (index === -1) throw new Error('分类不存在');
        mockCategories[index] = {
            ...mockCategories[index],
            ...categoryData,
            updated_at: new Date().toISOString()
        };
        return mockCategories[index];
    },
    async deleteCategory (id) {
        await delay(500);
        const index = mockCategories.findIndex((c)=>c.id === id);
        if (index === -1) throw new Error('分类不存在');
        mockCategories.splice(index, 1);
    }
};
// 当前登录用户（模拟会话）
let currentUser = null;
const mockUserApi = {
    async getCurrentUser () {
        await delay(200);
        return currentUser;
    },
    async signIn (email, password) {
        await delay(1000);
        const user = mockUsers.find((u)=>u.email === email && u.is_active !== false);
        if (!user) throw new Error('用户不存在或已被禁用');
        const storedPassword = mockPasswords[email];
        if (!storedPassword || storedPassword !== password) {
            throw new Error('密码错误');
        }
        currentUser = user;
        return {
            user
        };
    },
    async signOut () {
        await delay(300);
        currentUser = null;
    },
    async getAllUsers () {
        await delay(500);
        return [
            ...mockUsers
        ];
    },
    async createUser (userData) {
        await delay(800);
        // 检查邮箱是否已存在
        if (mockUsers.find((u)=>u.email === userData.email)) {
            throw new Error('邮箱已存在');
        }
        const newUser = {
            id: `user${Date.now()}`,
            email: userData.email,
            name: userData.name,
            role: userData.role,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            is_active: true
        };
        mockUsers.push(newUser);
        mockPasswords[userData.email] = userData.password;
        return newUser;
    },
    async updateUser (userData) {
        await delay(500);
        const index = mockUsers.findIndex((u)=>u.id === userData.id);
        if (index === -1) throw new Error('用户不存在');
        mockUsers[index] = {
            ...mockUsers[index],
            ...userData,
            updated_at: new Date().toISOString()
        };
        return mockUsers[index];
    },
    async deleteUser (userId) {
        await delay(500);
        const index = mockUsers.findIndex((u)=>u.id === userId);
        if (index === -1) throw new Error('用户不存在');
        const user = mockUsers[index];
        mockUsers.splice(index, 1);
        delete mockPasswords[user.email];
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "categoryApi": (()=>categoryApi),
    "newsApi": (()=>newsApi),
    "userApi": (()=>userApi)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mockData.ts [app-client] (ecmascript)");
;
;
// 检查是否有有效的 Supabase 配置
const isSupabaseConfigured = ()=>{
    // 如果是演示模式，直接使用模拟数据
    if ("TURBOPACK compile-time truthy", 1) {
        return false;
    }
    "TURBOPACK unreachable";
    const url = undefined;
    const key = undefined;
};
const newsApi = {
    // 获取新闻列表
    async getNews (filters = {}) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockNewsApi"].getNews(filters);
        }
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].NEWS).select(`
        *,
        category:categories(*),
        author:users(*)
      `).order('created_at', {
            ascending: false
        });
        if (filters.category_id) {
            query = query.eq('category_id', filters.category_id);
        }
        if (filters.status) {
            query = query.eq('status', filters.status);
        }
        if (filters.search) {
            query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`);
        }
        const limit = filters.limit || 10;
        const offset = ((filters.page || 1) - 1) * limit;
        query = query.range(offset, offset + limit - 1);
        const { data, error } = await query;
        if (error) throw error;
        return data;
    },
    // 获取单个新闻
    async getNewsById (id) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockNewsApi"].getNewsById(id);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].NEWS).select(`
        *,
        category:categories(*),
        author:users(*)
      `).eq('id', id).single();
        if (error) throw error;
        return data;
    },
    // 创建新闻
    async createNews (newsData) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockNewsApi"].createNews(newsData);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].NEWS).insert([
            {
                ...newsData,
                author_id: 'current_user_id',
                view_count: 0,
                published_at: newsData.status === 'published' ? new Date().toISOString() : null
            }
        ]).select().single();
        if (error) throw error;
        return data;
    },
    // 更新新闻
    async updateNews (newsData) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockNewsApi"].updateNews(newsData);
        }
        const { id, ...updateData } = newsData;
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].NEWS).update({
            ...updateData,
            updated_at: new Date().toISOString(),
            published_at: updateData.status === 'published' ? new Date().toISOString() : undefined
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // 删除新闻
    async deleteNews (id) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockNewsApi"].deleteNews(id);
        }
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].NEWS).delete().eq('id', id);
        if (error) throw error;
    },
    // 增加浏览量
    async incrementViewCount (id) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockNewsApi"].incrementViewCount(id);
        }
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].rpc('increment_view_count', {
            news_id: id
        });
        if (error) throw error;
    },
    // 批准新闻
    async approveNews (newsId) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockNewsApi"].approveNews(newsId);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].NEWS).update({
            status: 'published',
            published_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        }).eq('id', newsId).select().single();
        if (error) throw error;
        return data;
    },
    // 拒绝新闻
    async rejectNews (newsId, reason) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockNewsApi"].rejectNews(newsId, reason);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].NEWS).update({
            status: 'rejected',
            rejection_reason: reason,
            updated_at: new Date().toISOString()
        }).eq('id', newsId).select().single();
        if (error) throw error;
        return data;
    }
};
const categoryApi = {
    // 获取所有分类
    async getCategories () {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockCategoryApi"].getCategories();
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].CATEGORIES).select('*').order('name');
        if (error) throw error;
        return data;
    },
    // 创建分类
    async createCategory (categoryData) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockCategoryApi"].createCategory(categoryData);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].CATEGORIES).insert([
            categoryData
        ]).select().single();
        if (error) throw error;
        return data;
    },
    // 更新分类
    async updateCategory (id, categoryData) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockCategoryApi"].updateCategory(id, categoryData);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].CATEGORIES).update({
            ...categoryData,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // 删除分类
    async deleteCategory (id) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockCategoryApi"].deleteCategory(id);
        }
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].CATEGORIES).delete().eq('id', id);
        if (error) throw error;
    }
};
const userApi = {
    // 获取当前用户
    async getCurrentUser () {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockUserApi"].getCurrentUser();
        }
        const { data: { user } } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.getUser();
        return user;
    },
    // 登录
    async signIn (email, password) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockUserApi"].signIn(email, password);
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signInWithPassword({
            email,
            password
        });
        if (error) throw error;
        return data;
    },
    // 登出
    async signOut () {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockUserApi"].signOut();
        }
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.signOut();
        if (error) throw error;
    },
    // 获取所有用户（管理员功能）
    async getAllUsers () {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockUserApi"].getAllUsers();
        }
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].USERS).select('*').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    // 创建用户（管理员功能）
    async createUser (userData) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockUserApi"].createUser(userData);
        }
        // 在实际的 Supabase 实现中，这里需要使用 Supabase Auth Admin API
        // 或者通过服务端 API 来创建用户
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].USERS).insert([
            {
                email: userData.email,
                name: userData.name,
                role: userData.role,
                is_active: true
            }
        ]).select().single();
        if (error) throw error;
        return data;
    },
    // 更新用户（管理员功能）
    async updateUser (userData) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockUserApi"].updateUser(userData);
        }
        const { id, ...updateData } = userData;
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].USERS).update({
            ...updateData,
            updated_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    // 删除用户（管理员功能）
    async deleteUser (userId) {
        if (!isSupabaseConfigured()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mockData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockUserApi"].deleteUser(userId);
        }
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].from(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TABLES"].USERS).delete().eq('id', userId);
        if (error) throw error;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            checkUser();
        }
    }["AuthProvider.useEffect"], []);
    const checkUser = async ()=>{
        try {
            const currentUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userApi"].getCurrentUser();
            setUser(currentUser);
        } catch (error) {
            console.error('获取用户信息失败:', error);
            setUser(null);
        } finally{
            setLoading(false);
        }
    };
    const login = async (email, password)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userApi"].signIn(email, password);
            if (response.user) {
                setUser(response.user);
            }
        } catch (error) {
            console.error('登录失败:', error);
            throw error;
        }
    };
    const logout = async ()=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["userApi"].signOut();
            setUser(null);
        } catch (error) {
            console.error('登出失败:', error);
            throw error;
        }
    };
    const isAdmin = ()=>{
        return user?.role === 'admin';
    };
    const isEditor = ()=>{
        return user?.role === 'editor';
    };
    const value = {
        user,
        loading,
        login,
        logout,
        isAdmin,
        isEditor
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 69,
        columnNumber: 5
    }, this);
}
_s(AuthProvider, "NiO5z6JIqzX62LS5UWDgIqbZYyY=");
_c = AuthProvider;
function useAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_90953efc._.js.map